<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试图片加载</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-image {
            width: 100%;
            height: 400px;
            object-fit: cover;
            border: 2px solid #ccc;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>图片加载测试</h1>
        
        <h2>测试 hero-static.webp</h2>
        <img id="test-img" class="test-image" src="assets/images/hero-static.webp" alt="Hero Static Image">
        <div id="status" class="status">加载中...</div>
        
        <h2>文件信息</h2>
        <p>图片路径: <code>assets/images/hero-static.webp</code></p>
        <p>预期尺寸: 1920x1080 或类似</p>
    </div>

    <script>
        const img = document.getElementById('test-img');
        const status = document.getElementById('status');
        
        img.onload = function() {
            status.className = 'status success';
            status.innerHTML = `
                ✅ 图片加载成功<br>
                尺寸: ${this.naturalWidth} x ${this.naturalHeight}px<br>
                文件大小: 约 ${Math.round(this.naturalWidth * this.naturalHeight / 1024)}KB (估算)
            `;
        };
        
        img.onerror = function() {
            status.className = 'status error';
            status.innerHTML = '❌ 图片加载失败 - 请检查文件路径和文件是否存在';
        };
        
        // 检查文件是否存在
        fetch('assets/images/hero-static.webp')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.blob();
            })
            .then(blob => {
                console.log('文件存在，大小:', Math.round(blob.size / 1024), 'KB');
            })
            .catch(error => {
                console.error('文件检查失败:', error);
                if (img.complete && img.naturalWidth === 0) {
                    status.className = 'status error';
                    status.innerHTML = `❌ 文件不存在或无法访问: ${error.message}`;
                }
            });
    </script>
</body>
</html>
