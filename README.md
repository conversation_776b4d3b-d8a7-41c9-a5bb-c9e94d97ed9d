# 湖北银科官网项目

## 开发环境设置

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

然后在浏览器中访问 http://localhost:3000

## Stagewise 开发工具

本项目已集成 Stagewise 开发工具，可在开发环境中使用。Stagewise 是一个浏览器工具栏，可以连接前端 UI 到代码编辑器中的 AI 代理，允许开发人员选择网页中的元素，添加注释，并让 AI 代理根据上下文进行修改。

### 使用方法

1. 确保在开发环境中运行项目（localhost 或 .local 域名）
2. Stagewise 工具栏将自动加载
3. 使用工具栏选择元素并添加注释
4. AI 代理将根据上下文进行修改

### 注意事项

- Stagewise 工具栏仅在开发环境中加载，不会包含在生产构建中
- 如果工具栏未显示，请检查浏览器控制台是否有错误信息 