<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>颜色优化对比页面</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <style>
        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        
        @media (max-width: 768px) {
            .comparison-container {
                grid-template-columns: 1fr;
            }
        }
        
        .comparison-box {
            background: linear-gradient(135deg, #001122 0%, #003366 100%);
            padding: 2rem;
            border-radius: 16px;
            position: relative;
            min-height: 300px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }
        
        .comparison-label {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: rgba(0, 122, 255, 0.2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .old-subtitle {
            color: #a1a1a6 !important;
        }
        
        .old-button-secondary {
            background: transparent !important;
            border: 1px solid #38383a !important;
            color: white !important;
            padding: 12px 24px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .section-divider {
            margin: 4rem 0;
            text-align: center;
        }
        
        .section-divider h2 {
            color: var(--color-secondary);
            margin-bottom: 1rem;
        }
        
        .feature-highlight {
            background: rgba(0, 122, 255, 0.1);
            border: 1px solid rgba(0, 122, 255, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            color: var(--color-text-secondary);
        }
        
        .feature-list li::before {
            content: "✓";
            color: var(--color-accent);
            font-weight: bold;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="section-divider">
            <h1>颜色设计优化对比</h1>
            <p style="color: var(--color-text-secondary);">
                对比优化前后的视觉效果，提升用户体验和品牌一致性
            </p>
        </div>
        
        <!-- 主要对比区域 -->
        <div class="comparison-container">
            <!-- 优化后 -->
            <div class="comparison-box">
                <div class="comparison-label">✨ 优化后</div>
                <h1 class="hero-title" style="margin-bottom: 1.5rem;">
                    <span class="title-line">科技赋能金融</span>
                    <span class="title-line">智慧护航担保</span>
                </h1>
                <p class="hero-subtitle" style="margin-bottom: 2rem;">
                    湖北银科融资担保有限公司致力于运用大数据与AI技术<br>
                    为公积金贷款提供专业、安全、高效的担保服务
                </p>
                <div class="hero-cta">
                    <button class="cta-primary" style="margin-bottom: 1rem;">了解我们的服务</button>
                    <button class="cta-secondary">立即咨询</button>
                </div>
            </div>
            
            <!-- 优化前 -->
            <div class="comparison-box">
                <div class="comparison-label">📋 优化前</div>
                <h1 style="color: #f5f5f7; margin-bottom: 1.5rem; font-size: 2.5rem; font-weight: 600; line-height: 1.2;">
                    <span style="display: block;">科技赋能金融</span>
                    <span style="display: block;">智慧护航担保</span>
                </h1>
                <p class="old-subtitle" style="margin-bottom: 2rem; line-height: 1.6;">
                    湖北银科融资担保有限公司致力于运用大数据与AI技术<br>
                    为公积金贷款提供专业、安全、高效的担保服务
                </p>
                <div style="display: flex; flex-direction: column; gap: 1rem; align-items: center;">
                    <button style="background: var(--gradient-primary); color: white; border: none; padding: 12px 24px; border-radius: 12px; width: 200px;">了解我们的服务</button>
                    <button class="old-button-secondary" style="width: 200px;">立即咨询</button>
                </div>
            </div>
        </div>
        
        <!-- 优化说明 -->
        <div class="feature-highlight">
            <h3 style="color: var(--color-secondary); margin-bottom: 1rem;">🎨 设计优化要点</h3>
            <ul class="feature-list">
                <li><strong>副标题颜色优化：</strong>从 #a1a1a6 调整为 #b3b3b8，提升在深色背景下的可读性</li>
                <li><strong>次要按钮重设计：</strong>添加渐变背景和毛玻璃效果，增强视觉层次</li>
                <li><strong>按钮交互优化：</strong>添加悬停动画和阴影效果，提升用户体验</li>
                <li><strong>文字阴影增强：</strong>多层阴影确保文字在任何背景下都清晰可见</li>
                <li><strong>色彩系统完善：</strong>新增专用颜色变量，保持设计一致性</li>
            </ul>
        </div>
        
        <!-- 技术细节 -->
        <div class="section-divider">
            <h2>技术实现细节</h2>
        </div>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
            <div style="background: var(--color-surface); padding: 2rem; border-radius: 12px;">
                <h4 style="color: var(--color-secondary); margin-bottom: 1rem;">CSS变量优化</h4>
                <pre style="background: #000; padding: 1rem; border-radius: 8px; overflow-x: auto; font-size: 0.85rem;"><code>/* 新增颜色变量 */
--hero-subtitle-color: #b3b3b8;
--gradient-button-secondary: 
  linear-gradient(135deg, 
    rgba(0, 122, 255, 0.1) 0%, 
    rgba(88, 86, 214, 0.1) 100%);
</code></pre>
            </div>
            
            <div style="background: var(--color-surface); padding: 2rem; border-radius: 12px;">
                <h4 style="color: var(--color-secondary); margin-bottom: 1rem;">按钮样式增强</h4>
                <pre style="background: #000; padding: 1rem; border-radius: 8px; overflow-x: auto; font-size: 0.85rem;"><code>.cta-secondary {
  background: var(--gradient-button-secondary);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 122, 255, 0.3);
  box-shadow: 0 2px 10px rgba(0, 122, 255, 0.1);
}
</code></pre>
            </div>
        </div>
        
        <!-- 实际效果展示 -->
        <div class="section-divider">
            <h2>实际页面效果</h2>
            <p style="color: var(--color-text-secondary);">
                <a href="index.html" style="color: var(--color-secondary);">点击查看主页面实际效果</a>
            </p>
        </div>
    </div>
    
    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
