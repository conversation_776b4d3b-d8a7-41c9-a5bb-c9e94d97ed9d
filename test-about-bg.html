<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Section 背景测试</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/components/timeline.css">
    <style>
        body {
            margin: 0;
            padding: 0;
        }
        
        .test-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .test-section {
            min-height: 50vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
        }
        
        #about {
            min-height: 100vh;
        }
        
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
        
        /* 简化的统计数据样式 */
        .about-stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .stat-item {
            text-align: center;
            padding: 1rem;
            background: linear-gradient(135deg, 
                rgba(0, 15, 30, 0.85) 0%, 
                rgba(0, 25, 50, 0.8) 100%
            );
            border: 1px solid rgba(48, 209, 88, 0.2);
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #30D158;
        }
        
        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-section" style="background: #333;">
            <h1>滚动到下方查看 About Section 背景效果</h1>
        </div>
        
        <!-- About Section -->
        <section id="about" class="about-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">关于湖北银科</h2>
                    <p class="section-subtitle">十余年专业担保经验，科技创新引领行业发展</p>
                </div>
                <div class="about-content">
                    <!-- 简化的时间轴 -->
                    <div class="timeline-container" id="about-timeline">
                        <div class="timeline-line" style="--timeline-progress: 80%;"></div>
                        
                        <div class="timeline-item timeline-item--left timeline-item--animated timeline-item--milestone">
                            <div class="timeline-content">
                                <div class="timeline-date">
                                    <span class="timeline-year">2009</span>
                                    <span class="timeline-month">成立</span>
                                </div>
                                <h3 class="timeline-title">公司成立</h3>
                                <p class="timeline-description">湖北银科融资担保有限公司正式成立，注册资本1亿元</p>
                            </div>
                            <div class="timeline-marker">
                                <span class="timeline-icon">🏢</span>
                            </div>
                        </div>
                        
                        <div class="timeline-item timeline-item--right timeline-item--animated timeline-item--achievement">
                            <div class="timeline-content">
                                <div class="timeline-date">
                                    <span class="timeline-year">2015</span>
                                    <span class="timeline-month">发展</span>
                                </div>
                                <h3 class="timeline-title">业务拓展</h3>
                                <p class="timeline-description">开始专注公积金贷款担保业务，建立专业团队</p>
                            </div>
                            <div class="timeline-marker">
                                <span class="timeline-icon">📈</span>
                            </div>
                        </div>
                        
                        <div class="timeline-item timeline-item--left timeline-item--animated timeline-item--milestone">
                            <div class="timeline-content">
                                <div class="timeline-date">
                                    <span class="timeline-year">2020</span>
                                    <span class="timeline-month">创新</span>
                                </div>
                                <h3 class="timeline-title">科技转型</h3>
                                <p class="timeline-description">引入大数据和AI技术，建立智能风控体系</p>
                            </div>
                            <div class="timeline-marker">
                                <span class="timeline-icon">🤖</span>
                            </div>
                        </div>
                        
                        <div class="timeline-item timeline-item--right timeline-item--animated timeline-item--current">
                            <div class="timeline-content">
                                <div class="timeline-date">
                                    <span class="timeline-year">2025</span>
                                    <span class="timeline-month">现在</span>
                                </div>
                                <h3 class="timeline-title">行业领先</h3>
                                <p class="timeline-description">成为区域内领先的科技型担保公司</p>
                            </div>
                            <div class="timeline-marker">
                                <span class="timeline-icon">⭐</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 统计数据 -->
                    <div class="about-stats">
                        <div class="stat-item">
                            <div class="stat-number">1亿</div>
                            <div class="stat-label">注册资本</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">2009</div>
                            <div class="stat-label">成立年份</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">15+</div>
                            <div class="stat-label">行业经验</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <div class="test-section" style="background: #666;">
            <h1>About Section 下方</h1>
        </div>
    </div>
    
    <div class="debug-info">
        <div>背景状态: <span id="bg-status">未加载</span></div>
        <div>图片路径: <span id="img-path">-</span></div>
        <div>错误信息: <span id="error-msg">无</span></div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/core/ResourceManager.js"></script>
    <script src="assets/js/components/SectionBackgroundManager.js"></script>
    
    <script>
        // 简化的测试脚本
        class AboutBackgroundTest {
            constructor() {
                this.resourceManager = null;
                this.sectionBackgroundManager = null;
                
                this.init();
            }
            
            async init() {
                try {
                    console.log('🧪 开始 About Section 背景测试...');
                    
                    // 1. 初始化资源管理器
                    this.resourceManager = new ResourceManager({
                        basePath: 'assets/',
                        debug: true
                    });
                    
                    // 2. 初始化背景管理器
                    this.sectionBackgroundManager = new SectionBackgroundManager({
                        resourceManager: this.resourceManager,
                        debug: true
                    });
                    
                    // 3. 更新调试信息
                    this.updateDebugInfo('初始化完成', 'assets/images/backgrounds/about-bg.svg');
                    
                    console.log('✅ 测试初始化完成');
                    
                } catch (error) {
                    console.error('❌ 测试初始化失败:', error);
                    this.updateDebugInfo('初始化失败', '', error.message);
                }
            }
            
            updateDebugInfo(status, imgPath, error = '') {
                document.getElementById('bg-status').textContent = status;
                document.getElementById('img-path').textContent = imgPath;
                document.getElementById('error-msg').textContent = error;
            }
        }
        
        // 页面加载完成后启动测试
        document.addEventListener('DOMContentLoaded', () => {
            new AboutBackgroundTest();
        });
    </script>
</body>
</html>
