# 湖北银科官网图片资源获取指南

**创建日期**: 2025年1月11日
**用途**: 为网站提供必需的图片资源
**状态**: 待下载

## 🎯 必需图片资源清单

### 📋 优先级P0 - 立即需要

#### 1. Favicon图标

- **文件名**: `favicon.ico`
- **路径**: `assets/images/icons/favicon.ico`
- **尺寸**: 32x32, 16x16 (多尺寸ICO文件)
- **获取方式**:
  - 使用在线工具将现有logo.svg转换为ICO
  - 推荐网站: https://favicon.io/favicon-converter/
  - 上传: `assets/images/icons/logo.svg`
  - 下载生成的favicon.ico

#### 2. Hero静态背景

- **文件名**: `hero-static.webp`
- **路径**: `assets/images/hero-static.webp`
- **尺寸**: 1920x1080
- **推荐图片**:
  - Unsplash: https://unsplash.com/photos/eybM9n4yrpE
  - 描述: 黑色背景蓝色光波抽象图
  - 下载步骤: 访问链接 → 点击"Download free" → 选择"Large (1920×1080)"

#### 3. 中文字体文件

- **文件名**: `SourceHanSansCN-Regular.woff2`
- **路径**: `assets/fonts/SourceHanSansCN-Regular.woff2`
- **获取方式**:
  - Google Fonts: https://fonts.google.com/noto/specimen/Noto+Sans+SC
  - 或使用系统字体: 删除HTML中的字体预加载行

### 📋 优先级P1 - 重要资源

#### 4. Hero背景图片

- **文件名**: `hero-bg.webp`
- **路径**: `assets/images/backgrounds/hero-bg.webp`
- **尺寸**: 1920x1080
- **推荐图片**:
  - Unsplash搜索: "abstract technology blue"
  - 直接链接: https://unsplash.com/s/photos/abstract-technology
  - 选择深色背景、蓝色科技感的图片

#### 5. 区域背景图片

- **文件名**: `section-bg-1.webp`, `section-bg-2.webp`
- **路径**: `assets/images/backgrounds/`
- **尺寸**: 1920x1080
- **推荐图片**:
  - Pexels搜索: "data visualization"
  - 直接链接: https://www.pexels.com/search/data%20visualization/
  - 选择图表、数据分析相关图片

#### 6. Hero动画序列 (基础30帧)

- **文件名**: `frame_001.webp` 到 `frame_030.webp`
- **路径**: `assets/images/hero-sequence/`
- **尺寸**: 1280x720 (优化性能)
- **获取方式**:
  - 使用AI工具生成动画序列
  - 或使用静态图片制作简单的渐变动画

## 🔧 具体下载步骤

### 步骤1: 创建Favicon

```bash
# 访问 https://favicon.io/favicon-converter/
# 上传 assets/images/icons/logo.svg
# 下载生成的favicon.ico
# 保存到 assets/images/icons/favicon.ico
```

### 步骤2: 下载Hero背景

```bash
# 1. 访问 https://unsplash.com/photos/eybM9n4yrpE
# 2. 点击 "Download free"
# 3. 选择 "Large (1920×1080)"
# 4. 重命名为 hero-static.webp
# 5. 保存到 assets/images/hero-static.webp
```

### 步骤3: 下载技术背景图片

```bash
# 访问 https://unsplash.com/s/photos/abstract-technology
# 选择合适的图片，建议选择:
# - 深色背景
# - 蓝色科技元素
# - 抽象几何图案
# 下载并重命名为 hero-bg.webp
```

### 步骤4: 下载数据可视化图片

```bash
# 访问 https://www.pexels.com/search/data%20visualization/
# 选择2张不同的图片:
# - 图表数据分析
# - 金融科技场景
# 下载并重命名为 section-bg-1.webp, section-bg-2.webp
```

## 📐 图片处理要求

### 格式转换

- **目标格式**: WebP (优化加载速度)
- **转换工具**:
  - 在线工具: https://convertio.co/jpg-webp/
  - 本地工具: Adobe Photoshop, GIMP

### 尺寸优化

- **Hero图片**: 1920x1080 (桌面) + 1280x720 (移动端)
- **背景图片**: 1920x1080
- **压缩质量**: 80-90% (平衡质量与文件大小)

### 色彩调整

- **主色调**: 深色背景 (#000000, #1d1d1f)
- **强调色**: 科技蓝 (#007aff)
- **辅助色**: 成功绿 (#30d158)

## 🎨 推荐的具体图片

### Hero区域推荐

1. **抽象科技背景**

   - Unsplash ID: eybM9n4yrpE
   - 描述: 黑色背景蓝色光波
   - 适用: hero-static.webp
2. **数据网络连接**

   - 搜索: "network connections blue"
   - 适用: hero-bg.webp

### 业务区域推荐

1. **金融数据图表**

   - Pexels搜索: "financial charts"
   - 适用: section-bg-1.webp
2. **AI人工智能**

   - Unsplash搜索: "artificial intelligence"
   - 适用: section-bg-2.webp

## 🚀 快速实施方案

### 最小可行方案 (MVP)

如果时间紧急，可以先实现：

1. ✅ 使用favicon.io生成favicon.ico (5分钟)
2. ✅ 下载1张hero-static.webp (5分钟)
3. ✅ 删除字体预加载，使用系统字体 (1分钟)

### 完整方案

1. 下载所有推荐图片 (30分钟)
2. 格式转换为WebP (15分钟)
3. 尺寸优化和压缩 (15分钟)
4. 测试和调整 (15分钟)

## 📝 下载检查清单

- [ ] favicon.ico (32x32, 16x16)
- [X] hero-static.webp (1920x1080)
- [ ] hero-bg.webp (1920x1080)
- [ ] section-bg-1.webp (1920x1080)
- [ ] section-bg-2.webp (1920x1080)
- [ ] SourceHanSansCN-Regular.woff2 (可选)

## 🔗 有用的工具链接

- **Favicon生成**: https://favicon.io/favicon-converter/
- **图片格式转换**: https://convertio.co/
- **图片压缩**: https://tinypng.com/
- **免费图片**:
  - https://unsplash.com/
  - https://www.pexels.com/
  - https://pixabay.com/
- **字体下载**: https://fonts.google.com/

---

**注意**: 所有推荐的图片都来自免费资源网站，可以商业使用。下载时请确认许可证信息。

**完成后**: 将下载的图片放入对应目录，然后取消main.js中相关资源的注释，启用完整的图片加载功能。
