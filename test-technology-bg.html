<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Technology Section 背景测试</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/components/data-visualization.css">
    <style>
        body {
            margin: 0;
            padding: 0;
        }
        
        .test-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .test-section {
            min-height: 50vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
        }
        
        #technology {
            min-height: 100vh;
        }
        
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-section" style="background: #333;">
            <h1>滚动到下方查看 Technology Section 背景效果</h1>
        </div>
        
        <!-- Technology Section -->
        <section id="technology" class="technology-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">科技创新实力</h2>
                    <p class="section-subtitle">大数据与AI技术驱动的智能风控体系</p>
                </div>
                <div class="technology-content" id="technology-content">
                    <!-- 内容将由 DataVisualization 组件动态生成 -->
                </div>
            </div>
        </section>
        
        <div class="test-section" style="background: #666;">
            <h1>Technology Section 上方</h1>
        </div>
    </div>
    
    <div class="debug-info">
        <div>背景状态: <span id="bg-status">未加载</span></div>
        <div>图片路径: <span id="img-path">-</span></div>
        <div>错误信息: <span id="error-msg">无</span></div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/core/PerformanceDetector.js"></script>
    <script src="assets/js/core/ResourceManager.js"></script>
    <script src="assets/js/core/ScrollAnimationEngine.js"></script>
    <script src="assets/js/components/DataVisualization.js"></script>
    <script src="assets/js/components/SectionBackgroundManager.js"></script>
    
    <script>
        // 简化的测试脚本
        class TechBackgroundTest {
            constructor() {
                this.resourceManager = null;
                this.sectionBackgroundManager = null;
                this.dataVisualization = null;
                
                this.init();
            }
            
            async init() {
                try {
                    console.log('🧪 开始 Technology Section 背景测试...');
                    
                    // 1. 初始化资源管理器
                    this.resourceManager = new ResourceManager({
                        basePath: 'assets/',
                        debug: true
                    });
                    
                    // 2. 初始化数据可视化组件
                    this.dataVisualization = new DataVisualization();
                    
                    // 3. 初始化背景管理器
                    this.sectionBackgroundManager = new SectionBackgroundManager({
                        resourceManager: this.resourceManager,
                        debug: true
                    });
                    
                    // 4. 更新调试信息
                    this.updateDebugInfo('初始化完成', 'assets/images/backgrounds/technology-bg.svg');
                    
                    console.log('✅ 测试初始化完成');
                    
                } catch (error) {
                    console.error('❌ 测试初始化失败:', error);
                    this.updateDebugInfo('初始化失败', '', error.message);
                }
            }
            
            updateDebugInfo(status, imgPath, error = '') {
                document.getElementById('bg-status').textContent = status;
                document.getElementById('img-path').textContent = imgPath;
                document.getElementById('error-msg').textContent = error;
            }
        }
        
        // 页面加载完成后启动测试
        document.addEventListener('DOMContentLoaded', () => {
            new TechBackgroundTest();
        });
        
        // 监听背景加载事件
        document.addEventListener('click', (e) => {
            if (e.target.textContent.includes('生成背景')) {
                // 手动触发背景生成（如果需要）
                if (window.BackgroundImageGenerator) {
                    const generator = new BackgroundImageGenerator();
                    generator.generateAndDownload('technology-bg-test.webp');
                }
            }
        });
    </script>
</body>
</html>
