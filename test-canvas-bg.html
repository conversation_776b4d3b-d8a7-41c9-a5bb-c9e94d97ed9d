<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas 背景测试</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/components/timeline.css">
    <link rel="stylesheet" href="assets/css/components/data-visualization.css">
    <style>
        body {
            margin: 0;
            padding: 0;
        }
        
        .test-container {
            min-height: 300vh;
        }
        
        .test-section {
            min-height: 50vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
        }
        
        #technology, #about {
            min-height: 100vh;
        }
        
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
            max-width: 300px;
        }
        
        .performance-info {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
        
        .scroll-test {
            background: linear-gradient(45deg, #333, #666);
            color: white;
            text-align: center;
            padding: 2rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-section scroll-test">
            <div>
                <h1>Canvas 背景性能测试</h1>
                <p>快速滑动页面测试 Canvas 背景的性能表现</p>
            </div>
        </div>
        
        <!-- Technology Section -->
        <section id="technology" class="technology-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">科技创新实力</h2>
                    <p class="section-subtitle">Canvas 渲染的科技背景</p>
                </div>
                <div class="technology-content" id="technology-content">
                    <!-- 简化内容 -->
                    <div class="tech-visualization">
                        <div class="data-stats-grid">
                            <div class="data-stat-card data-stat-card--animated">
                                <div class="stat-icon">📊</div>
                                <div class="stat-content">
                                    <div class="stat-value">
                                        <span class="stat-number">1000000</span>
                                        <span class="stat-unit">+</span>
                                    </div>
                                    <h4 class="stat-title">数据处理量</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <div class="test-section scroll-test">
            <h2>滑动测试区域 - 观察性能</h2>
        </div>
        
        <!-- About Section -->
        <section id="about" class="about-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">关于湖北银科</h2>
                    <p class="section-subtitle">Canvas 渲染的企业发展背景</p>
                </div>
                <div class="about-content">
                    <!-- 简化的时间轴 -->
                    <div class="timeline-container">
                        <div class="timeline-line" style="--timeline-progress: 80%;"></div>
                        
                        <div class="timeline-item timeline-item--left timeline-item--animated timeline-item--milestone">
                            <div class="timeline-content">
                                <div class="timeline-date">
                                    <span class="timeline-year">2009</span>
                                    <span class="timeline-month">成立</span>
                                </div>
                                <h3 class="timeline-title">公司成立</h3>
                                <p class="timeline-description">湖北银科融资担保有限公司正式成立</p>
                            </div>
                            <div class="timeline-marker">
                                <span class="timeline-icon">🏢</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <div class="test-section scroll-test">
            <h2>测试完成 - 检查性能数据</h2>
        </div>
    </div>
    
    <div class="debug-info">
        <div><strong>Canvas 背景状态</strong></div>
        <div>Technology: <span id="tech-status">未加载</span></div>
        <div>About: <span id="about-status">未加载</span></div>
        <div>错误: <span id="error-msg">无</span></div>
    </div>
    
    <div class="performance-info">
        <div><strong>性能监控</strong></div>
        <div>FPS: <span id="fps">0</span></div>
        <div>滑动事件: <span id="scroll-events">0</span></div>
        <div>重绘次数: <span id="repaints">0</span></div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/core/ResourceManager.js"></script>
    <script src="assets/js/components/SectionBackgroundManager.js"></script>
    
    <script>
        // Canvas 背景测试脚本
        class CanvasBackgroundTest {
            constructor() {
                this.resourceManager = null;
                this.sectionBackgroundManager = null;
                this.performanceMonitor = {
                    fps: 0,
                    scrollEvents: 0,
                    repaints: 0,
                    lastTime: performance.now()
                };
                
                this.init();
                this.startPerformanceMonitoring();
            }
            
            async init() {
                try {
                    console.log('🧪 开始 Canvas 背景测试...');
                    
                    // 1. 初始化资源管理器
                    this.resourceManager = new ResourceManager({
                        basePath: 'assets/',
                        debug: true
                    });
                    
                    // 2. 初始化背景管理器
                    this.sectionBackgroundManager = new SectionBackgroundManager({
                        resourceManager: this.resourceManager,
                        debug: true
                    });
                    
                    // 3. 监听背景加载状态
                    this.monitorBackgroundLoading();
                    
                    console.log('✅ Canvas 背景测试初始化完成');
                    
                } catch (error) {
                    console.error('❌ 测试初始化失败:', error);
                    this.updateDebugInfo('', '', error.message);
                }
            }
            
            monitorBackgroundLoading() {
                // 检查背景加载状态
                const checkStatus = () => {
                    const techCanvas = document.querySelector('#technology .section-background-canvas');
                    const aboutCanvas = document.querySelector('#about .section-background-canvas');
                    
                    const techStatus = techCanvas ? '已加载 Canvas' : '未加载';
                    const aboutStatus = aboutCanvas ? '已加载 Canvas' : '未加载';
                    
                    document.getElementById('tech-status').textContent = techStatus;
                    document.getElementById('about-status').textContent = aboutStatus;
                };
                
                // 定期检查状态
                setInterval(checkStatus, 1000);
                checkStatus();
            }
            
            startPerformanceMonitoring() {
                // FPS 监控
                const measureFPS = () => {
                    const now = performance.now();
                    const delta = now - this.performanceMonitor.lastTime;
                    this.performanceMonitor.fps = Math.round(1000 / delta);
                    this.performanceMonitor.lastTime = now;
                    
                    document.getElementById('fps').textContent = this.performanceMonitor.fps;
                    requestAnimationFrame(measureFPS);
                };
                measureFPS();
                
                // 滑动事件监控
                let scrollEventCount = 0;
                window.addEventListener('scroll', () => {
                    scrollEventCount++;
                    document.getElementById('scroll-events').textContent = scrollEventCount;
                }, { passive: true });
                
                // 重绘监控（简化版）
                let repaintCount = 0;
                const observer = new MutationObserver(() => {
                    repaintCount++;
                    document.getElementById('repaints').textContent = repaintCount;
                });
                
                observer.observe(document.body, {
                    childList: true,
                    subtree: true,
                    attributes: true
                });
            }
            
            updateDebugInfo(techStatus, aboutStatus, error = '') {
                if (techStatus) document.getElementById('tech-status').textContent = techStatus;
                if (aboutStatus) document.getElementById('about-status').textContent = aboutStatus;
                document.getElementById('error-msg').textContent = error;
            }
        }
        
        // 页面加载完成后启动测试
        document.addEventListener('DOMContentLoaded', () => {
            new CanvasBackgroundTest();
        });
        
        // 窗口大小调整测试
        window.addEventListener('resize', () => {
            console.log('🔄 窗口大小调整，测试 Canvas 重绘...');
        });
    </script>
</body>
</html>
