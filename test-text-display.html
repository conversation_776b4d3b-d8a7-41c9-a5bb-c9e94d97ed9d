<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文字显示测试页面</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <style>
        /* 测试样式 */
        .test-section {
            padding: 2rem;
            margin: 2rem 0;
            border: 2px solid var(--color-secondary);
            border-radius: var(--border-radius-lg);
            background: var(--color-surface);
        }
        
        .test-title {
            color: var(--color-secondary);
            margin-bottom: 1rem;
        }
        
        .test-description {
            color: var(--color-text-secondary);
            margin-bottom: 1rem;
        }
        
        .debug-info {
            background: rgba(0, 122, 255, 0.1);
            padding: 1rem;
            border-radius: var(--border-radius-md);
            font-family: monospace;
            font-size: 0.9rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; margin: 2rem 0; color: var(--color-text-primary);">
            文字显示问题诊断测试页面
        </h1>
        
        <!-- 测试1: Hero区域文字显示 -->
        <div class="test-section">
            <h2 class="test-title">测试1: Hero区域文字显示</h2>
            <p class="test-description">模拟主页Hero区域的文字显示情况</p>
            
            <section class="hero-section" style="min-height: 60vh; position: relative;">
                <div class="hero-canvas-container">
                    <canvas id="test-canvas" class="hero-canvas" style="background: linear-gradient(135deg, #001122 0%, #003366 100%);"></canvas>
                </div>
                <div class="hero-content">
                    <h1 class="hero-title">
                        <span class="title-line">科技赋能金融</span>
                        <span class="title-line">智慧护航担保</span>
                    </h1>
                    <p class="hero-subtitle">
                        湖北银科融资担保有限公司致力于运用大数据与AI技术<br>
                        为公积金贷款提供专业、安全、高效的担保服务
                    </p>
                    <div class="hero-cta">
                        <button class="cta-primary">
                            了解我们的服务
                        </button>
                        <button class="cta-secondary">
                            立即咨询
                        </button>
                    </div>
                </div>
            </section>
            
            <div class="debug-info">
                <strong>调试信息:</strong><br>
                - Hero标题z-index: <span id="title-zindex"></span><br>
                - Hero副标题z-index: <span id="subtitle-zindex"></span><br>
                - Canvas z-index: <span id="canvas-zindex"></span><br>
                - 标题可见性: <span id="title-visibility"></span><br>
                - 副标题可见性: <span id="subtitle-visibility"></span>
            </div>
        </div>
        
        <!-- 测试2: 文字层级测试 -->
        <div class="test-section">
            <h2 class="test-title">测试2: 文字层级测试</h2>
            <p class="test-description">测试不同z-index值下的文字显示</p>
            
            <div style="position: relative; height: 200px; background: linear-gradient(45deg, #333, #666);">
                <!-- 背景层 -->
                <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); z-index: 1;"></div>
                
                <!-- 文字层 -->
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 10; text-align: center;">
                    <h3 style="color: white; text-shadow: 0 2px 4px rgba(0,0,0,0.8); margin: 0;">
                        这段文字应该清晰可见
                    </h3>
                    <p style="color: #ccc; text-shadow: 0 1px 2px rgba(0,0,0,0.8); margin: 0.5rem 0 0 0;">
                        副标题文字也应该可以正常显示
                    </p>
                </div>
            </div>
        </div>
        
        <!-- 测试3: CSS样式检查 */
        <div class="test-section">
            <h2 class="test-title">测试3: CSS样式检查</h2>
            <p class="test-description">检查关键CSS样式是否正确应用</p>
            
            <div class="debug-info" id="css-check">
                正在检查CSS样式...
            </div>
        </div>
    </div>

    <script>
        // 调试信息收集
        function collectDebugInfo() {
            const titleElement = document.querySelector('.hero-title');
            const subtitleElement = document.querySelector('.hero-subtitle');
            const canvasElement = document.querySelector('#test-canvas');
            
            if (titleElement) {
                const titleStyle = window.getComputedStyle(titleElement);
                document.getElementById('title-zindex').textContent = titleStyle.zIndex;
                document.getElementById('title-visibility').textContent = 
                    `opacity: ${titleStyle.opacity}, visibility: ${titleStyle.visibility}`;
            }
            
            if (subtitleElement) {
                const subtitleStyle = window.getComputedStyle(subtitleElement);
                document.getElementById('subtitle-zindex').textContent = subtitleStyle.zIndex;
                document.getElementById('subtitle-visibility').textContent = 
                    `opacity: ${subtitleStyle.opacity}, visibility: ${subtitleStyle.visibility}`;
            }
            
            if (canvasElement) {
                const canvasStyle = window.getComputedStyle(canvasElement);
                document.getElementById('canvas-zindex').textContent = canvasStyle.zIndex;
            }
        }
        
        // CSS样式检查
        function checkCSSStyles() {
            const checks = [];
            
            // 检查hero-title样式
            const titleElement = document.querySelector('.hero-title');
            if (titleElement) {
                const style = window.getComputedStyle(titleElement);
                checks.push(`Hero标题 - z-index: ${style.zIndex}, opacity: ${style.opacity}, color: ${style.color}`);
                checks.push(`Hero标题 - text-shadow: ${style.textShadow}`);
            }
            
            // 检查hero-subtitle样式
            const subtitleElement = document.querySelector('.hero-subtitle');
            if (subtitleElement) {
                const style = window.getComputedStyle(subtitleElement);
                checks.push(`Hero副标题 - z-index: ${style.zIndex}, opacity: ${style.opacity}, color: ${style.color}`);
            }
            
            // 检查canvas样式
            const canvasElement = document.querySelector('#test-canvas');
            if (canvasElement) {
                const style = window.getComputedStyle(canvasElement);
                checks.push(`Canvas - z-index: ${style.zIndex}, pointer-events: ${style.pointerEvents}`);
            }
            
            document.getElementById('css-check').innerHTML = checks.join('<br>');
        }
        
        // 页面加载完成后执行检查
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                collectDebugInfo();
                checkCSSStyles();
            }, 100);
        });
        
        // 设置测试canvas
        const testCanvas = document.getElementById('test-canvas');
        if (testCanvas) {
            const ctx = testCanvas.getContext('2d');
            
            function resizeCanvas() {
                const container = testCanvas.parentElement;
                if (container) {
                    const rect = container.getBoundingClientRect();
                    testCanvas.width = rect.width;
                    testCanvas.height = rect.height;
                    
                    // 绘制测试背景
                    const gradient = ctx.createLinearGradient(0, 0, testCanvas.width, testCanvas.height);
                    gradient.addColorStop(0, '#001122');
                    gradient.addColorStop(1, '#003366');
                    ctx.fillStyle = gradient;
                    ctx.fillRect(0, 0, testCanvas.width, testCanvas.height);
                    
                    // 添加轻微遮罩
                    const overlay = ctx.createLinearGradient(0, 0, 0, testCanvas.height);
                    overlay.addColorStop(0, 'rgba(0, 0, 0, 0.15)');
                    overlay.addColorStop(0.5, 'rgba(0, 0, 0, 0.1)');
                    overlay.addColorStop(1, 'rgba(0, 0, 0, 0.2)');
                    ctx.fillStyle = overlay;
                    ctx.fillRect(0, 0, testCanvas.width, testCanvas.height);
                }
            }
            
            resizeCanvas();
            window.addEventListener('resize', resizeCanvas);
        }
    </script>
</body>
</html>
