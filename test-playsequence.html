<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试 playSequence 方法</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .hero-section {
            position: relative;
            height: 60vh;
            background: #333;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        .hero-canvas-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .hero-canvas {
            width: 100%;
            height: 100%;
            display: block;
        }
        .controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .btn:hover {
            background: #0056b3;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试 ImageSequencePlayer.playSequence 方法</h1>
        
        <div class="hero-section">
            <div class="hero-canvas-container">
                <canvas id="hero-canvas" class="hero-canvas"></canvas>
            </div>
        </div>
        
        <div class="controls">
            <h3>控制面板</h3>
            <button class="btn" onclick="testPlaySequence()">测试 playSequence</button>
            <button class="btn" onclick="testWithTwoImages()">使用两张图片测试</button>
            <button class="btn" onclick="pauseAnimation()">暂停动画</button>
            <button class="btn" onclick="resumeAnimation()">恢复动画</button>
            <button class="btn" onclick="clearDebug()">清除调试信息</button>
        </div>
        
        <div class="status" id="status">等待测试...</div>
        
        <div class="debug-info" id="debug-info">
            <strong>调试信息:</strong><br>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="assets/js/core/ImageSequencePlayer.js"></script>
    
    <script>
        let player = null;
        
        function log(message, type = 'info') {
            const debugInfo = document.getElementById('debug-info');
            const timestamp = new Date().toLocaleTimeString();
            debugInfo.innerHTML += `[${timestamp}] ${message}<br>`;
            debugInfo.scrollTop = debugInfo.scrollHeight;
            
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            
            console.log(message);
        }
        
        function clearDebug() {
            document.getElementById('debug-info').innerHTML = '<strong>调试信息:</strong><br>';
        }
        
        function testPlaySequence() {
            log('开始测试 playSequence 方法...', 'info');

            try {
                // 获取 canvas 元素
                const canvas = document.getElementById('hero-canvas');
                if (!canvas) {
                    log('❌ Canvas 元素未找到', 'error');
                    return;
                }

                // 创建 ImageSequencePlayer 实例
                log('创建 ImageSequencePlayer 实例...', 'info');
                player = new ImageSequencePlayer(canvas, null, {
                    fitMode: 'cover',
                    alignment: 'center',
                    autoPreload: true,
                    debug: true,
                    onLoadProgress: (progress, loaded, total) => {
                        log(`图片加载进度: ${Math.round(progress * 100)}% (${loaded}/${total})`, 'info');
                    },
                    onLoadComplete: (result) => {
                        log(`图片加载完成: ${JSON.stringify(result)}`, result.success ? 'success' : 'error');

                        if (result.success && result.loaded > 0) {
                            log('开始播放序列动画...', 'info');
                            player.playSequence(0, result.loaded - 1, {
                                duration: 2000,
                                loop: 2
                            });
                        }
                    }
                });

                log('✅ ImageSequencePlayer 实例创建成功', 'success');

                // 设置图片序列（使用现有的两张图片）
                const imageUrls = [
                    'assets/images/hero-sequence/frame_001.webp',
                    'assets/images/hero-sequence/frame_002.webp'
                ];

                log('设置图片序列...', 'info');
                log(`图片URL: ${imageUrls.join(', ')}`, 'info');
                player.setImages(imageUrls);

            } catch (error) {
                log(`❌ 测试失败: ${error.message}`, 'error');
                console.error(error);
            }
        }
        
        function testWithTwoImages() {
            log('使用两张图片进行测试...', 'info');
            testPlaySequence();
        }
        
        function pauseAnimation() {
            if (player) {
                player.pauseSequence();
                log('⏸️ 动画已暂停', 'info');
            } else {
                log('❌ 播放器未初始化', 'error');
            }
        }
        
        function resumeAnimation() {
            if (player) {
                player.resumeSequence();
                log('▶️ 动画已恢复', 'info');
            } else {
                log('❌ 播放器未初始化', 'error');
            }
        }
        
        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            log('页面加载完成，准备测试...', 'info');
        });
    </script>
</body>
</html>
