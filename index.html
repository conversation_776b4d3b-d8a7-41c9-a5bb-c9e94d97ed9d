<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="湖北银科融资担保有限公司 - 专业从事公积金贷款担保服务，运用大数据和AI技术进行风险防控，为客户提供安全可靠的金融担保服务">
    <meta name="keywords" content="湖北银科,融资担保,公积金贷款,大数据风控,AI技术,商转公,二手房担保">
    <meta name="author" content="湖北银科融资担保有限公司">
    
    <!-- {{CHENGQI:
    // Action: Added
    // Timestamp: 2025-01-10 11:30:00 +08:00
    // Reason: Per P1-AR-001 创建标准HTML5结构，包含SEO优化和基础安全配置
    // Principle_Applied: KISS - 简洁清晰的HTML结构，避免不必要的复杂性
    // Optimization: 预加载关键资源，优化首屏加载性能
    // Architectural_Note (AR): 遵循HTML5语义化标准，为后续组件开发提供基础结构
    // Documentation_Note (DW): 相关架构文档见 /project_document/architecture/final_architecture_v1.0.md
    // }} -->
    
    <title>湖北银科融资担保有限公司 - 专业公积金贷款担保服务</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="assets/css/main.css" as="style">
    <link rel="preload" href="assets/js/main.js" as="script">
    <!-- <link rel="preload" href="assets/fonts/SourceHanSansCN-Regular.woff2" as="font" type="font/woff2" crossorigin> -->
    
    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">
    
    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="SAMEORIGIN">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/images/icons/logo.svg">
    <link rel="alternate icon" href="assets/images/icons/favicon.ico">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <link rel="stylesheet" href="assets/css/components/timeline.css">
    <link rel="stylesheet" href="assets/css/components/business-cards.css">
    <link rel="stylesheet" href="assets/css/components/data-visualization.css">
    <link rel="stylesheet" href="assets/css/components/contact-form.css">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <img src="assets/images/icons/logo.svg" alt="湖北银科" width="80" height="80">
            </div>
            <div class="loading-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <p class="loading-text">正在加载银科官网...</p>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav id="main-navigation" class="main-nav">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="assets/images/icons/logo.svg" alt="湖北银科" width="40" height="40">
                <span class="logo-text">湖北银科</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link active">首页</a></li>
                <li><a href="#about" class="nav-link">关于我们</a></li>
                <li><a href="#services" class="nav-link">业务服务</a></li>
                <li><a href="#technology" class="nav-link">科技实力</a></li>
                <li><a href="#partnership" class="nav-link">合作案例</a></li>
                <li><a href="#contact" class="nav-link">联系我们</a></li>
            </ul>
            <div class="nav-toggle" id="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content" class="main-content">
        
        <!-- Hero Section -->
        <section id="home" class="hero-section">
            <div class="hero-canvas-container">
                <canvas id="hero-canvas" class="hero-canvas"></canvas>
            </div>
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="title-line">科技赋能金融</span>
                    <span class="title-line">智慧护航担保</span>
                </h1>
                <p class="hero-subtitle">
                    湖北银科融资担保有限公司致力于运用大数据与AI技术<br>
                    为公积金贷款提供专业、安全、高效的担保服务
                </p>
                <div class="hero-cta">
                    <button class="cta-primary" onclick="scrollToSection('services')">
                        了解我们的服务
                    </button>
                    <button class="cta-secondary" onclick="scrollToSection('contact')">
                        立即咨询
                    </button>
                </div>
            </div>
            <div class="hero-scroll-indicator">
                <div class="scroll-mouse">
                    <div class="scroll-wheel"></div>
                </div>
                <span class="scroll-text">向下滚动探索更多</span>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="about-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">关于湖北银科</h2>
                    <p class="section-subtitle">十余年专业担保经验，科技创新引领行业发展</p>
                </div>
                <div class="about-content">
                    <div class="about-timeline" id="about-timeline">
                        <!-- Timeline content will be dynamically generated -->
                    </div>
                    <div class="about-stats">
                        <div class="stat-item">
                            <span class="stat-number" data-target="100000000">0</span>
                            <span class="stat-unit">元</span>
                            <span class="stat-label">注册资本</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" data-target="2009">0</span>
                            <span class="stat-unit">年</span>
                            <span class="stat-label">成立时间</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" data-target="15">0</span>
                            <span class="stat-unit">年</span>
                            <span class="stat-label">行业经验</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section id="services" class="services-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">核心业务服务</h2>
                    <p class="section-subtitle">四大核心业务，全面覆盖公积金贷款担保需求</p>
                </div>
                <div class="services-grid" id="services-grid">
                    <!-- Service cards will be dynamically generated -->
                </div>
            </div>
        </section>

        <!-- Technology Section -->
        <section id="technology" class="technology-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">科技创新实力</h2>
                    <p class="section-subtitle">大数据与AI技术驱动的智能风控体系</p>
                </div>
                <div class="technology-content" id="technology-content">
                    <!-- Technology visualization will be dynamically generated -->
                </div>
            </div>
        </section>

        <!-- Partnership Section -->
        <section id="partnership" class="partnership-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">合作伙伴</h2>
                    <p class="section-subtitle">与多地住房公积金中心建立深度合作关系</p>
                </div>
                <div class="partnership-content" id="partnership-content">
                    <!-- Partnership map and logos will be dynamically generated -->
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="contact-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">联系我们</h2>
                    <p class="section-subtitle">期待与您携手合作，共创数字担保新未来</p>
                </div>
                <div class="contact-content">
                    <div class="contact-form-container">
                        <form id="contact-form" class="contact-form">
                            <!-- Contact form will be generated by ContactForm component -->
                        </form>
                    </div>
                    <div class="contact-info">
                        <div class="contact-item">
                            <h4>公司地址</h4>
                            <p>湖北省宜昌市</p>
                        </div>
                        <div class="contact-item">
                            <h4>联系电话</h4>
                            <p>400-XXX-XXXX</p>
                        </div>
                        <div class="contact-item">
                            <h4>电子邮箱</h4>
                            <p><EMAIL></p>
                        </div>
                        <div class="contact-item">
                            <h4>营业时间</h4>
                            <p>周一至周五 9:00-17:30</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="main-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <img src="assets/images/icons/logo.svg" alt="湖北银科" width="40" height="40">
                        <span>湖北银科融资担保有限公司</span>
                    </div>
                    <p class="footer-description">
                        专业的公积金贷款担保服务提供商，运用大数据与AI技术<br>
                        为客户提供安全、高效的金融担保解决方案
                    </p>
                </div>
                <div class="footer-section">
                    <h4>业务服务</h4>
                    <ul>
                        <li><a href="#services">公积金贷款风险筛查</a></li>
                        <li><a href="#services">商转公担保业务</a></li>
                        <li><a href="#services">二手房担保业务</a></li>
                        <li><a href="#services">贷后资产管理</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>关于我们</h4>
                    <ul>
                        <li><a href="#about">公司简介</a></li>
                        <li><a href="#technology">技术实力</a></li>
                        <li><a href="#partnership">合作伙伴</a></li>
                        <li><a href="#contact">联系我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 湖北银科融资担保有限公司. 保留所有权利.</p>
                <p>融资担保许可证号：鄂420502020135</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="assets/js/core/PerformanceDetector.js"></script>
    <script src="assets/js/core/ResourceManager.js"></script>
    <script src="assets/js/core/ScrollAnimationEngine.js"></script>
    <script src="assets/js/core/ImageSequencePlayer.js"></script>
    <script src="assets/js/components/HeroAnimation.js"></script>
    <script src="assets/js/components/TimelineSection.js"></script>
    <script src="assets/js/components/BusinessCards.js"></script>
    <script src="assets/js/components/DataVisualization.js"></script>
    <script src="assets/js/components/ContactForm.js"></script>
    <script src="assets/js/components/SectionBackgroundManager.js"></script>
    <script src="assets/js/utils/BackgroundImageGenerator.js"></script>
    <script src="assets/js/main.js"></script>
    
    <!-- Stagewise Dev Toolbar (only loads in development) -->
    <script src="assets/js/stagewise-init.js"></script>
</body>
</html> 