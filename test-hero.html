<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试Hero动画</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        .hero-section {
            position: relative;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            overflow: hidden;
        }
        .hero-canvas-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            padding: 0 20px;
            color: white;
        }
        .hero-title {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        .debug-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div class="debug-info" id="debug-info">
        <div>调试信息:</div>
        <div id="debug-content">初始化中...</div>
    </div>

    <section id="home" class="hero-section">
        <div class="hero-canvas-container">
            <canvas id="hero-canvas" class="hero-canvas"></canvas>
        </div>
        <div class="hero-content">
            <h1 class="hero-title">
                <span class="title-line">科技赋能金融</span><br>
                <span class="title-line">智慧护航担保</span>
            </h1>
            <p class="hero-subtitle">
                湖北银科融资担保有限公司致力于运用大数据与AI技术<br>
                为公积金贷款提供专业、安全、高效的担保服务
            </p>
        </div>
    </section>

    <!-- 加载必要的脚本 -->
    <script src="assets/js/core/PerformanceDetector.js"></script>
    <script src="assets/js/core/ResourceManager.js"></script>
    <script src="assets/js/core/ScrollAnimationEngine.js"></script>
    <script src="assets/js/core/ImageSequencePlayer.js"></script>
    <script src="assets/js/components/HeroAnimation.js"></script>

    <script>
        // 调试信息显示
        function updateDebugInfo(message) {
            const debugContent = document.getElementById('debug-content');
            debugContent.innerHTML += '<br>' + message;
            console.log(message);
        }

        // 初始化测试
        async function initTest() {
            try {
                updateDebugInfo('开始初始化测试...');

                // 1. 初始化性能检测器
                const performanceDetector = new PerformanceDetector({
                    autoRun: false,
                    onComplete: (level, metrics) => {
                        updateDebugInfo(`性能级别: ${level}`);
                        updateDebugInfo(`FPS: ${metrics.fps}`);
                    }
                });

                const performanceLevel = await performanceDetector.runTest();
                updateDebugInfo(`检测到性能级别: ${performanceLevel}`);

                // 2. 初始化资源管理器
                const resourceManager = new ResourceManager({
                    basePath: 'assets/',
                    onProgress: (progress, loaded, total) => {
                        updateDebugInfo(`资源加载: ${Math.round(progress * 100)}%`);
                    },
                    onComplete: (result) => {
                        updateDebugInfo(`资源加载完成: ${result.loaded}/${result.total}`);
                    }
                });

                // 3. 根据性能级别加载资源
                if (performanceLevel === 'low') {
                    resourceManager.add('images/hero-static.webp', 'image', 'hero-static', 1);
                    updateDebugInfo('添加静态图片资源');
                } else {
                    updateDebugInfo('高/中性能设备，应该加载序列图片');
                }

                // 4. 开始加载资源
                updateDebugInfo('开始加载资源...');
                await resourceManager.loadAll();

                // 5. 初始化Hero动画
                updateDebugInfo('初始化Hero动画组件...');
                const heroAnimation = new HeroAnimation({
                    performanceLevel: performanceLevel,
                    resourceManager: resourceManager,
                    scrollEngine: null,
                    debug: true
                });

                updateDebugInfo('Hero动画组件初始化完成');

                // 6. 检查资源管理器中的图片
                const staticImage = resourceManager.get('hero-static', 'image');
                if (staticImage) {
                    updateDebugInfo('✅ 静态图片已加载到资源管理器');
                } else {
                    updateDebugInfo('❌ 静态图片未在资源管理器中找到');
                }

            } catch (error) {
                updateDebugInfo(`❌ 错误: ${error.message}`);
                console.error('初始化错误:', error);
            }
        }

        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', initTest);
    </script>
</body>
</html>
