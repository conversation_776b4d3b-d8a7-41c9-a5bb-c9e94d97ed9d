<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 外圆环 -->
  <circle cx="40" cy="40" r="38" stroke="url(#gradient1)" stroke-width="2" fill="none"/>
  
  <!-- 内圆环 -->
  <circle cx="40" cy="40" r="28" stroke="url(#gradient2)" stroke-width="1.5" fill="none" opacity="0.8"/>
  
  <!-- 中心图标 - 银科的"银"字抽象化 -->
  <g transform="translate(40, 40)">
    <!-- 主体结构 -->
    <path d="M-12 -8 L12 -8 M-8 -12 L-8 12 M8 -12 L8 12 M-12 8 L12 8" 
          stroke="url(#gradient3)" 
          stroke-width="2" 
          stroke-linecap="round"/>
    
    <!-- 科技感装饰点 -->
    <circle cx="-6" cy="-6" r="1.5" fill="#007AFF"/>
    <circle cx="6" cy="-6" r="1.5" fill="#007AFF"/>
    <circle cx="-6" cy="6" r="1.5" fill="#007AFF"/>
    <circle cx="6" cy="6" r="1.5" fill="#007AFF"/>
    
    <!-- 中心核心 -->
    <circle cx="0" cy="0" r="3" fill="url(#gradient4)"/>
  </g>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#007AFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5856D6;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#30D158;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#007AFF;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F5F5F7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A1A1A6;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="gradient4" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#007AFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5856D6;stop-opacity:1" />
    </radialGradient>
  </defs>
</svg>
