<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0a0a0a;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="60%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f3460;stop-opacity:1" />
    </linearGradient>
    
    <!-- 发光效果 -->
    <radialGradient id="blueGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#007AFF;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#007AFF;stop-opacity:0" />
    </radialGradient>
    
    <radialGradient id="greenGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#30D158;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#30D158;stop-opacity:0" />
    </radialGradient>
    
    <radialGradient id="purpleGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#5856D6;stop-opacity:0.25" />
      <stop offset="100%" style="stop-color:#5856D6;stop-opacity:0" />
    </radialGradient>
    
    <!-- 滤镜效果 -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景渐变 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 网格线 -->
  <g stroke="rgba(0,122,255,0.1)" stroke-width="1" fill="none">
    <!-- 垂直线 -->
    <line x1="80" y1="0" x2="80" y2="1080"/>
    <line x1="160" y1="0" x2="160" y2="1080"/>
    <line x1="240" y1="0" x2="240" y2="1080"/>
    <line x1="320" y1="0" x2="320" y2="1080"/>
    <line x1="400" y1="0" x2="400" y2="1080"/>
    <line x1="480" y1="0" x2="480" y2="1080"/>
    <line x1="560" y1="0" x2="560" y2="1080"/>
    <line x1="640" y1="0" x2="640" y2="1080"/>
    <line x1="720" y1="0" x2="720" y2="1080"/>
    <line x1="800" y1="0" x2="800" y2="1080"/>
    <line x1="880" y1="0" x2="880" y2="1080"/>
    <line x1="960" y1="0" x2="960" y2="1080"/>
    <line x1="1040" y1="0" x2="1040" y2="1080"/>
    <line x1="1120" y1="0" x2="1120" y2="1080"/>
    <line x1="1200" y1="0" x2="1200" y2="1080"/>
    <line x1="1280" y1="0" x2="1280" y2="1080"/>
    <line x1="1360" y1="0" x2="1360" y2="1080"/>
    <line x1="1440" y1="0" x2="1440" y2="1080"/>
    <line x1="1520" y1="0" x2="1520" y2="1080"/>
    <line x1="1600" y1="0" x2="1600" y2="1080"/>
    <line x1="1680" y1="0" x2="1680" y2="1080"/>
    <line x1="1760" y1="0" x2="1760" y2="1080"/>
    <line x1="1840" y1="0" x2="1840" y2="1080"/>
    
    <!-- 水平线 -->
    <line x1="0" y1="80" x2="1920" y2="80"/>
    <line x1="0" y1="160" x2="1920" y2="160"/>
    <line x1="0" y1="240" x2="1920" y2="240"/>
    <line x1="0" y1="320" x2="1920" y2="320"/>
    <line x1="0" y1="400" x2="1920" y2="400"/>
    <line x1="0" y1="480" x2="1920" y2="480"/>
    <line x1="0" y1="560" x2="1920" y2="560"/>
    <line x1="0" y1="640" x2="1920" y2="640"/>
    <line x1="0" y1="720" x2="1920" y2="720"/>
    <line x1="0" y1="800" x2="1920" y2="800"/>
    <line x1="0" y1="880" x2="1920" y2="880"/>
    <line x1="0" y1="960" x2="1920" y2="960"/>
  </g>
  
  <!-- 发光圆圈 -->
  <circle cx="384" cy="324" r="150" fill="url(#blueGlow)"/>
  <circle cx="1536" cy="756" r="200" fill="url(#greenGlow)"/>
  <circle cx="1152" cy="216" r="100" fill="url(#purpleGlow)"/>
  
  <!-- 发光圆圈边框 -->
  <circle cx="384" cy="324" r="120" fill="none" stroke="rgba(0,122,255,0.5)" stroke-width="2" filter="url(#glow)"/>
  <circle cx="1536" cy="756" r="160" fill="none" stroke="rgba(48,209,88,0.4)" stroke-width="2" filter="url(#glow)"/>
  <circle cx="1152" cy="216" r="80" fill="none" stroke="rgba(88,86,214,0.45)" stroke-width="2" filter="url(#glow)"/>
  
  <!-- 数据流线条 -->
  <g stroke="rgba(0,122,255,0.4)" stroke-width="2" fill="none" filter="url(#glow)">
    <path d="M0,324 Q576,216 1152,648 T1920,540"/>
    <path d="M0,756 Q576,432 1152,864 T1920,432"/>
    <path d="M384,0 Q576,540 1152,324 Q1536,756 1536,1080"/>
  </g>
  
  <!-- 几何图形 -->
  <g stroke="rgba(48,209,88,0.3)" stroke-width="2" fill="rgba(48,209,88,0.1)">
    <!-- 六边形 -->
    <polygon points="192,108 252,138 252,198 192,228 132,198 132,138" filter="url(#glow)"/>
    <polygon points="1728,108 1808,148 1808,228 1728,268 1648,228 1648,148" filter="url(#glow)"/>
    <polygon points="192,972 262,1012 262,1092 192,1132 122,1092 122,1012" filter="url(#glow)"/>
    <polygon points="1728,972 1778,1002 1778,1062 1728,1092 1678,1062 1678,1002" filter="url(#glow)"/>
  </g>
  
  <!-- 内部小圆 -->
  <circle cx="192" cy="168" r="18" fill="rgba(48,209,88,0.1)"/>
  <circle cx="1728" cy="188" r="24" fill="rgba(48,209,88,0.1)"/>
  <circle cx="192" cy="1032" r="21" fill="rgba(48,209,88,0.1)"/>
  <circle cx="1728" cy="1032" r="15" fill="rgba(48,209,88,0.1)"/>
</svg>
