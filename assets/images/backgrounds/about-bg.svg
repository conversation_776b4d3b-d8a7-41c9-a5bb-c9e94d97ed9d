<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 - 企业发展主题 -->
    <linearGradient id="aboutBgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f0f0f;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2c2c2e;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#1c1c1e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0a0a0a;stop-opacity:1" />
    </linearGradient>
    
    <!-- 发光效果 - 企业成长主题 -->
    <radialGradient id="growthGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#30D158;stop-opacity:0.25" />
      <stop offset="100%" style="stop-color:#30D158;stop-opacity:0" />
    </radialGradient>
    
    <radialGradient id="innovationGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#007AFF;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#007AFF;stop-opacity:0" />
    </radialGradient>
    
    <radialGradient id="teamGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FF9500;stop-opacity:0.15" />
      <stop offset="100%" style="stop-color:#FF9500;stop-opacity:0" />
    </radialGradient>
    
    <!-- 滤镜效果 -->
    <filter id="softGlow">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 时间线渐变 -->
    <linearGradient id="timelineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#30D158;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#007AFF;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#FF9500;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  
  <!-- 背景渐变 -->
  <rect width="1920" height="1080" fill="url(#aboutBgGradient)"/>
  
  <!-- 企业发展时间线 - 主要视觉元素 -->
  <g stroke="url(#timelineGradient)" stroke-width="3" fill="none" filter="url(#softGlow)">
    <!-- 主时间线 -->
    <path d="M100,540 Q480,400 960,540 Q1440,680 1820,540" stroke-width="4"/>
    
    <!-- 发展节点 -->
    <circle cx="300" cy="480" r="8" fill="#30D158" stroke="#30D158" stroke-width="2"/>
    <circle cx="600" cy="520" r="10" fill="#007AFF" stroke="#007AFF" stroke-width="2"/>
    <circle cx="960" cy="540" r="12" fill="#FF9500" stroke="#FF9500" stroke-width="2"/>
    <circle cx="1320" cy="580" r="10" fill="#30D158" stroke="#30D158" stroke-width="2"/>
    <circle cx="1620" cy="560" r="8" fill="#007AFF" stroke="#007AFF" stroke-width="2"/>
  </g>
  
  <!-- 企业成长发光区域 -->
  <circle cx="300" cy="300" r="120" fill="url(#growthGlow)"/>
  <circle cx="960" cy="200" r="150" fill="url(#innovationGlow)"/>
  <circle cx="1620" cy="350" r="100" fill="url(#teamGlow)"/>
  
  <!-- 网络连接线 - 象征团队协作 -->
  <g stroke="rgba(0,122,255,0.15)" stroke-width="1" fill="none">
    <!-- 连接网络 -->
    <line x1="300" y1="300" x2="600" y2="200"/>
    <line x1="600" y1="200" x2="960" y2="200"/>
    <line x1="960" y1="200" x2="1320" y2="250"/>
    <line x1="1320" y1="250" x2="1620" y2="350"/>
    
    <!-- 交叉连接 -->
    <line x1="300" y1="300" x2="960" y2="200"/>
    <line x1="600" y1="200" x2="1320" y2="250"/>
    <line x1="960" y1="200" x2="1620" y2="350"/>
    
    <!-- 垂直连接 -->
    <line x1="300" y1="300" x2="300" y2="480"/>
    <line x1="600" y1="200" x2="600" y2="520"/>
    <line x1="960" y1="200" x2="960" y2="540"/>
    <line x1="1320" y1="250" x2="1320" y2="580"/>
    <line x1="1620" y1="350" x2="1620" y2="560"/>
  </g>
  
  <!-- 企业建筑轮廓 - 象征公司发展 -->
  <g stroke="rgba(48,209,88,0.3)" stroke-width="2" fill="rgba(48,209,88,0.05)">
    <!-- 建筑群 -->
    <rect x="150" y="700" width="60" height="120" rx="4"/>
    <rect x="230" y="680" width="80" height="140" rx="4"/>
    <rect x="330" y="650" width="70" height="170" rx="4"/>
    
    <rect x="800" y="720" width="90" height="100" rx="4"/>
    <rect x="910" y="690" width="100" height="130" rx="4"/>
    <rect x="1030" y="660" width="80" height="160" rx="4"/>
    
    <rect x="1450" y="740" width="70" height="80" rx="4"/>
    <rect x="1540" y="710" width="90" height="110" rx="4"/>
    <rect x="1650" y="680" width="75" height="140" rx="4"/>
  </g>
  
  <!-- 建筑窗户 - 增加细节 -->
  <g fill="rgba(0,122,255,0.4)">
    <!-- 第一组建筑 -->
    <rect x="165" y="720" width="8" height="8" rx="1"/>
    <rect x="180" y="720" width="8" height="8" rx="1"/>
    <rect x="165" y="740" width="8" height="8" rx="1"/>
    <rect x="180" y="740" width="8" height="8" rx="1"/>
    
    <rect x="250" y="700" width="10" height="10" rx="1"/>
    <rect x="270" y="700" width="10" height="10" rx="1"/>
    <rect x="290" y="700" width="10" height="10" rx="1"/>
    <rect x="250" y="720" width="10" height="10" rx="1"/>
    <rect x="270" y="720" width="10" height="10" rx="1"/>
    <rect x="290" y="720" width="10" height="10" rx="1"/>
    
    <!-- 第二组建筑 -->
    <rect x="820" y="740" width="12" height="12" rx="1"/>
    <rect x="840" y="740" width="12" height="12" rx="1"/>
    <rect x="860" y="740" width="12" height="12" rx="1"/>
    
    <rect x="930" y="710" width="15" height="15" rx="2"/>
    <rect x="955" y="710" width="15" height="15" rx="2"/>
    <rect x="980" y="710" width="15" height="15" rx="2"/>
    <rect x="930" y="735" width="15" height="15" rx="2"/>
    <rect x="955" y="735" width="15" height="15" rx="2"/>
    <rect x="980" y="735" width="15" height="15" rx="2"/>
  </g>
  
  <!-- 数据流动效果 - 象征信息化发展 -->
  <g stroke="rgba(0,122,255,0.3)" stroke-width="1" fill="none" filter="url(#softGlow)">
    <!-- 流动的数据线 -->
    <path d="M0,300 Q200,250 400,300 Q600,350 800,300 Q1000,250 1200,300 Q1400,350 1600,300 Q1800,250 1920,300">
      <animate attributeName="stroke-dasharray" values="0,20;20,0;0,20" dur="3s" repeatCount="indefinite"/>
    </path>
    
    <path d="M0,400 Q300,350 600,400 Q900,450 1200,400 Q1500,350 1920,400">
      <animate attributeName="stroke-dasharray" values="10,15;25,0;10,15" dur="4s" repeatCount="indefinite"/>
    </path>
    
    <path d="M0,600 Q400,550 800,600 Q1200,650 1920,600">
      <animate attributeName="stroke-dasharray" values="5,25;30,0;5,25" dur="5s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- 企业成长图表 - 象征业绩增长 -->
  <g stroke="rgba(48,209,88,0.6)" stroke-width="3" fill="none" filter="url(#softGlow)">
    <!-- 增长曲线 -->
    <path d="M100,900 L300,850 L500,800 L700,720 L900,650 L1100,600 L1300,550 L1500,500 L1700,450 L1900,400"/>
    
    <!-- 数据点 -->
    <circle cx="300" cy="850" r="4" fill="#30D158"/>
    <circle cx="500" cy="800" r="4" fill="#30D158"/>
    <circle cx="700" cy="720" r="4" fill="#30D158"/>
    <circle cx="900" cy="650" r="4" fill="#30D158"/>
    <circle cx="1100" cy="600" r="4" fill="#30D158"/>
    <circle cx="1300" cy="550" r="4" fill="#30D158"/>
    <circle cx="1500" cy="500" r="4" fill="#30D158"/>
    <circle cx="1700" cy="450" r="4" fill="#30D158"/>
  </g>
  
  <!-- 团队协作节点 -->
  <g fill="rgba(255,149,0,0.4)" stroke="rgba(255,149,0,0.6)" stroke-width="2">
    <!-- 团队节点 -->
    <circle cx="200" cy="150" r="6"/>
    <circle cx="250" cy="120" r="6"/>
    <circle cx="300" cy="140" r="6"/>
    <circle cx="350" cy="110" r="6"/>
    
    <circle cx="1400" cy="180" r="6"/>
    <circle cx="1450" cy="150" r="6"/>
    <circle cx="1500" cy="170" r="6"/>
    <circle cx="1550" cy="140" r="6"/>
    
    <!-- 连接线 -->
    <line x1="200" y1="150" x2="250" y2="120" stroke-width="1"/>
    <line x1="250" y1="120" x2="300" y2="140" stroke-width="1"/>
    <line x1="300" y1="140" x2="350" y2="110" stroke-width="1"/>
    <line x1="200" y1="150" x2="300" y2="140" stroke-width="1"/>
    
    <line x1="1400" y1="180" x2="1450" y2="150" stroke-width="1"/>
    <line x1="1450" y1="150" x2="1500" y2="170" stroke-width="1"/>
    <line x1="1500" y1="170" x2="1550" y2="140" stroke-width="1"/>
    <line x1="1400" y1="180" x2="1500" y2="170" stroke-width="1"/>
  </g>
  
  <!-- 创新火花效果 -->
  <g fill="rgba(0,122,255,0.6)">
    <!-- 小星星效果 -->
    <polygon points="960,100 965,110 975,110 967,118 970,128 960,122 950,128 953,118 945,110 955,110"/>
    <polygon points="400,180 403,186 409,186 405,190 407,196 400,193 393,196 395,190 391,186 397,186"/>
    <polygon points="1500,120 503,126 509,126 505,130 507,136 500,133 493,136 495,130 491,126 497,126"/>
    
    <!-- 动态闪烁效果 -->
    <circle cx="800" cy="150" r="2" fill="rgba(0,122,255,0.8)">
      <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1200" cy="180" r="2" fill="rgba(48,209,88,0.8)">
      <animate attributeName="opacity" values="1;0;1" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="600" cy="120" r="2" fill="rgba(255,149,0,0.8)">
      <animate attributeName="opacity" values="0;1;0" dur="2.5s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>
