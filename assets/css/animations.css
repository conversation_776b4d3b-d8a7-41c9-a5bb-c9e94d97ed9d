/* {{CHENGQI:
// Action: Added
// Timestamp: 2025-01-11 16:00:00 +08:00
// Reason: Per P1-AR-001 创建动画样式文件，支持Apple风格的动画效果
// Principle_Applied: SOLID - 样式模块化设计；DRY - 复用动画关键帧和过渡效果
// Optimization: 使用硬件加速的CSS动画，优化性能表现
// Architectural_Note (AR): 配合JavaScript动画引擎，提供流畅的视觉效果
// Documentation_Note (DW): 相关设计规范见 /project_document/architecture/final_architecture_v1.0.md
// }} */

/* ==========================================================================
   湖北银科官网动画样式文件 - Apple风格动画系统
   ========================================================================== */

/* 动画关键帧定义 */

/* 淡入向上动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* 淡入向下动画 */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -30px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* 淡入向左动画 */
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translate3d(-30px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* 淡入向右动画 */
@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translate3d(30px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* 缩放淡入动画 */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: translate3d(0, 0, 0) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0) scale(1);
  }
}

/* 旋转淡入动画 */
@keyframes fadeInRotate {
  from {
    opacity: 0;
    transform: translate3d(0, 0, 0) rotate(-5deg);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0) rotate(0deg);
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* 呼吸动画 */
@keyframes breathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

/* 浮动动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 发光动画 */
@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 122, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(0, 122, 255, 0.6);
  }
}

/* 渐变移动动画 */
@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 打字机效果 */
@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

/* 闪烁光标 */
@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* 动画工具类 */

/* 基础动画类 */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fade-in-down {
  animation: fadeInDown 0.8s ease-out forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.8s ease-out forwards;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.8s ease-out forwards;
}

.animate-fade-in-rotate {
  animation: fadeInRotate 0.8s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-breathe {
  animation: breathe 3s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* 延迟动画类 */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }
.animate-delay-600 { animation-delay: 0.6s; }
.animate-delay-700 { animation-delay: 0.7s; }
.animate-delay-800 { animation-delay: 0.8s; }
.animate-delay-900 { animation-delay: 0.9s; }
.animate-delay-1000 { animation-delay: 1s; }

/* 动画持续时间类 */
.animate-duration-fast { animation-duration: 0.3s; }
.animate-duration-normal { animation-duration: 0.5s; }
.animate-duration-slow { animation-duration: 1s; }
.animate-duration-slower { animation-duration: 2s; }

/* Hero区域动画 */
.hero-section {
  position: relative;
  overflow: hidden;
}

.hero-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 10; /* 提高z-index确保文字在最上层 */
  transform: translateZ(0); /* 启用硬件加速 */
  /* 确保文字始终可见 */
  opacity: 1 !important;
  visibility: visible !important;
}

.hero-title {
  opacity: 1; /* 默认可见，避免动画失败时不显示 */
  transform: translateY(0); /* 默认位置正常 */
  transition: all 1s ease-out;
  /* 确保文字可读性 */
  color: var(--color-text-primary) !important;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.9);
  z-index: 15;
  position: relative;
}

.hero-title.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.hero-title .title-line {
  display: block;
  overflow: hidden;
}

.hero-title .title-line::after {
  content: '';
  display: inline-block;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  animation: typewriter 1.5s steps(20) forwards;
  margin-left: 10px;
}

.hero-subtitle {
  opacity: 1; /* 默认可见，避免动画失败时不显示 */
  transform: translateY(0); /* 默认位置正常 */
  transition: all 1s ease-out 0.3s;
  /* 确保文字可读性 */
  color: var(--hero-subtitle-color) !important;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.8);
  z-index: 15;
  position: relative;
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
}

.hero-subtitle.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* 滚动指示器动画 */
.hero-scroll-indicator {
  animation: float 2s ease-in-out infinite;
}

.scroll-mouse {
  position: relative;
  width: 24px;
  height: 40px;
  border: 2px solid var(--color-text-secondary);
  border-radius: 12px;
  margin: 0 auto 8px;
}

.scroll-wheel {
  position: absolute;
  top: 6px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 6px;
  background: var(--color-text-secondary);
  border-radius: 1px;
  animation: scrollWheel 2s ease-in-out infinite;
}

@keyframes scrollWheel {
  0%, 20% {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(16px);
    opacity: 0;
  }
}

/* 导航栏动画 */
.main-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-header, 1000);
  background: var(--nav-background); /* 渐变半透明背景 */
  backdrop-filter: blur(15px) saturate(120%);
  -webkit-backdrop-filter: blur(15px) saturate(120%);
  border-bottom: 1px solid var(--nav-border);
  box-shadow: 0 0 30px rgba(207, 208, 212, 0.2);
  transition: all 0.3s ease-out;
  transform: translateZ(0); /* 启用硬件加速 */
  padding: var(--spacing-md) 0;
  min-height: 72px;
}

.main-nav.scrolled {
  background: var(--nav-background-scrolled); /* 滚动时增强透明度 */
  backdrop-filter: blur(20px) saturate(150%);
  -webkit-backdrop-filter: blur(20px) saturate(150%);
  box-shadow: 0 5px 30px rgba(0, 10, 60, 0.25);
  border-bottom: 1px solid var(--nav-border-scrolled);
}

.nav-link {
  position: relative;
  transition: color 0.3s ease-out;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
}

.nav-link:hover {
  background: rgba(0, 122, 255, 0.15);
  color: var(--color-text-primary);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  transition: width 0.3s ease-out;
  border-radius: 1px;
  box-shadow: 0 0 8px rgba(0, 122, 255, 0.5);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 80%;
}

.nav-link.active {
  background: rgba(0, 122, 255, 0.2);
  color: var(--color-text-primary);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.4);
}

/* 按钮动画 */
.cta-primary,
.cta-secondary {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease-out;
  transform: translateZ(0); /* 启用硬件加速 */
}

.cta-primary::before,
.cta-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease-out;
}

.cta-primary:hover::before,
.cta-secondary:hover::before {
  left: 100%;
}

.cta-primary:hover,
.cta-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3);
}

/* 卡片动画 */
.card {
  transition: all 0.3s ease-out;
  transform: translateZ(0); /* 启用硬件加速 */
}

.card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* 加载动画 */
.loading-screen {
  transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
}

.loading-screen.loaded {
  opacity: 0;
  visibility: hidden;
}

/* 响应式动画优化 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 移动端动画优化 */
@media (max-width: 768px) {
  .animate-fade-in-up,
  .animate-fade-in-down,
  .animate-fade-in-left,
  .animate-fade-in-right {
    animation-duration: 0.6s;
  }
  
  .hero-title,
  .hero-subtitle {
    transition-duration: 0.8s;
  }
}

/* 导航栏Logo效果 */
.nav-logo {
  transition: all 0.3s ease-out;
}

.nav-logo:hover {
  transform: scale(1.05);
}

.nav-logo img {
  filter: drop-shadow(0 0 8px rgba(0, 122, 255, 0.4));
  transition: filter 0.3s ease-out;
}

.nav-logo:hover img {
  filter: drop-shadow(0 0 12px rgba(0, 122, 255, 0.6));
}

.logo-text {
  background: var(--gradient-text);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: var(--font-weight-semibold);
  transition: all 0.3s ease-out;
}

/* 导航栏高度优化 */
.nav-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 响应式导航栏高度 */
@media (max-width: 768px) {
  .main-nav {
    padding: var(--spacing-sm) 0;
    min-height: 64px;
  }
}

/* 导航栏背景发光效果 */
.main-nav::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 50%, rgba(0, 122, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 50%, rgba(88, 86, 214, 0.1) 0%, transparent 40%);
  z-index: -1;
  opacity: 0.8;
  transition: opacity 0.5s ease-out;
}

.main-nav.scrolled::before {
  opacity: 0.5;
}
