/* {{CHENGQI:
// Action: Added
// Timestamp: 2025-01-10 11:35:00 +08:00
// Reason: Per P1-AR-001 创建主样式文件，建立Apple风格设计系统
// Principle_Applied: SOLID - 样式模块化设计，单一职责原则；DRY - 复用设计变量和mixins
// Optimization: CSS自定义属性实现主题系统，支持硬件加速的动画
// Architectural_Note (AR): 遵循Apple设计语言，深色主题配科技蓝色彩体系
// Documentation_Note (DW): 相关设计规范见 /project_document/architecture/final_architecture_v1.0.md
// }} */

/* ==========================================================================
   湖北银科官网主样式文件 - Apple风格设计系统
   ========================================================================== */

/* CSS变量 - 设计系统基础 */
:root {
  /* 色彩系统 - Apple风格深色主题 */
  --color-primary: #1d1d1f;           /* 主色调 - 深灰黑 */
  --color-secondary: #007aff;         /* 次要色 - 科技蓝 */
  --color-accent: #30d158;            /* 强调色 - 成功绿 */
  --color-text-primary: #f5f5f7;     /* 主文本色 - 浅灰白 */
  --color-text-secondary: #d1d1d6;   /* 次要文本色 - 优化为更亮的灰色，增强可读性 */
  --color-text-tertiary: #8e8e93;    /* 三级文本色 - 原来的中灰色 */
  --color-background: #000000;       /* 背景色 - 纯黑 */
  --color-surface: #1c1c1e;          /* 表面色 - 深灰 */
  --color-border: #38383a;           /* 边框色 - 中灰 */

  /* Hero区域专用颜色 */
  --hero-subtitle-color: #b3b3b8;    /* Hero副标题专用色 - 在深色背景上更清晰 */
  
  /* 渐变色彩 */
  --gradient-primary: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
  --gradient-secondary: linear-gradient(135deg, rgba(0, 122, 255, 0.15) 0%, rgba(88, 86, 214, 0.15) 100%);
  --gradient-surface: linear-gradient(135deg, #1c1c1e 0%, #2c2c2e 100%);
  --gradient-text: linear-gradient(90deg, #007aff 0%, #30d158 100%);

  /* 按钮专用渐变 */
  --gradient-button-secondary: linear-gradient(135deg, rgba(0, 122, 255, 0.1) 0%, rgba(88, 86, 214, 0.1) 100%);
  
  /* 导航栏专用色彩 - 与页面背景渐变融合 */
  --nav-background: linear-gradient(90deg, rgba(0, 10, 40, 0.4) 0%, rgba(0, 30, 80, 0.4) 100%);
  --nav-background-scrolled: linear-gradient(90deg, rgba(0, 10, 40, 0.75) 0%, rgba(0, 30, 80, 0.75) 100%);
  --nav-border: rgba(0, 122, 255, 0.08);
  --nav-border-scrolled: rgba(0, 122, 255, 0.15);
  
  /* 字体系统 */
  --font-family-primary: "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-secondary: "SF Pro Text", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-chinese: "PingFang SC", "Source Han Sans CN", "Microsoft YaHei", sans-serif;
  
  /* 字体大小 - 模块化缩放 */
  --font-size-xs: 0.75rem;      /* 12px */
  --font-size-sm: 0.875rem;     /* 14px */
  --font-size-base: 1rem;       /* 16px */
  --font-size-lg: 1.125rem;     /* 18px */
  --font-size-xl: 1.25rem;      /* 20px */
  --font-size-2xl: 1.5rem;      /* 24px */
  --font-size-3xl: 1.875rem;    /* 30px */
  --font-size-4xl: 2.25rem;     /* 36px */
  --font-size-5xl: 3rem;        /* 48px */
  --font-size-6xl: 3.75rem;     /* 60px */
  
  /* 字重 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* 间距系统 - 8px基础网格 */
  --spacing-xs: 0.25rem;        /* 4px */
  --spacing-sm: 0.5rem;         /* 8px */
  --spacing-md: 1rem;           /* 16px */
  --spacing-lg: 1.5rem;         /* 24px */
  --spacing-xl: 2rem;           /* 32px */
  --spacing-2xl: 3rem;          /* 48px */
  --spacing-3xl: 4rem;          /* 64px */
  --spacing-4xl: 6rem;          /* 96px */
  --spacing-5xl: 8rem;          /* 128px */
  
  /* 圆角系统 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-2xl: 24px;
  --border-radius-full: 9999px;
  
  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.7);
  
  /* 过渡动画 */
  --transition-fast: all 0.15s ease-out;
  --transition-normal: all 0.25s ease-out;
  --transition-slow: all 0.5s ease-out;
  
  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-header: 1010;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
  
  /* 布局断点 */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* 基础重置和标准化 */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.6;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family-chinese), var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
  color: var(--color-text-primary);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
  /* 移除padding-top，让hero区域从顶部开始 */
}

/* 主内容区域 */
.main-content {
  position: relative;
  z-index: 1;
}

/* 默认元素样式 */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-chinese), var(--font-family-primary);
  font-weight: var(--font-weight-semibold);
  line-height: 1.2;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
  margin-bottom: var(--spacing-md);
  color: var(--color-text-secondary);
}

a {
  color: var(--color-secondary);
  text-decoration: none;
  transition: var(--transition-fast);
}

a:hover {
  color: var(--color-accent);
  text-decoration: underline;
}

/* 按钮基础样式 */
button {
  font-family: inherit;
  font-size: inherit;
  border: none;
  outline: none;
  cursor: pointer;
  transition: var(--transition-normal);
}

/* 输入框基础样式 */
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  border: 1px solid var(--color-border);
  outline: none;
  transition: var(--transition-fast);
}

input:focus,
textarea:focus,
select:focus {
  border-color: var(--color-secondary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

/* 图片和媒体 */
img,
video,
canvas {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 列表样式 */
ul,
ol {
  list-style: none;
}

/* 通用容器 */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--spacing-xl);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--spacing-2xl);
  }
}

/* 节区间距 */
section {
  padding: var(--spacing-4xl) 0;
}

@media (min-width: 768px) {
  section {
    padding: var(--spacing-5xl) 0;
  }
}

/* 通用组件样式 */

/* 节区标题 */
.section-header {
  text-align: center;
  margin-bottom: var(--spacing-4xl);
}

.section-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  background: var(--gradient-text);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: var(--spacing-md);
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* 加载屏幕 */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--color-background);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
}

.loading-screen.hidden {
  opacity: 0;
  visibility: hidden;
}

.loading-content {
  text-align: center;
}

.loading-logo {
  margin-bottom: var(--spacing-xl);
  animation: pulse 2s ease-in-out infinite;
}

.loading-progress {
  width: 200px;
}

.progress-bar {
  width: 100%;
  height: 2px;
  background: var(--color-border);
  border-radius: var(--border-radius-full);
  overflow: hidden;
  margin-bottom: var(--spacing-md);
}

.progress-fill {
  width: 0%;
  height: 100%;
  background: var(--gradient-primary);
  transition: width 0.3s ease-out;
}

.loading-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* 动画关键帧 */
@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.8; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 硬件加速优化 */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 隐藏/显示工具类 */
.hidden {
  display: none !important;
}

.invisible {
  visibility: hidden !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 响应式工具类 */
@media (max-width: 767px) {
  .hidden-mobile { display: none !important; }
}

@media (min-width: 768px) {
  .hidden-tablet { display: none !important; }
}

@media (min-width: 1024px) {
  .hidden-desktop { display: none !important; }
}

/* 无障碍访问优化 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 特定section样式 */

/* Hero Section */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  overflow: hidden;
  /* 确保hero区域从页面顶部开始，没有空隙 */
  margin-top: 0;
  padding-top: 0;
}

.hero-canvas-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  /* 确保canvas容器完全覆盖hero区域，没有间隙 */
  margin: 0;
  padding: 0;
}

.hero-content {
  position: relative;
  z-index: 10; /* 提高z-index确保文字在最上层 */
  max-width: 800px;
  padding: 0 var(--spacing-lg);
  /* 顶部间距在responsive.css中根据设备类型设置 */
  /* 确保文字始终可见的备用样式 */
  color: var(--color-text-primary) !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8); /* 添加文字阴影增强可读性 */
}

/* About Section */
.about-section {
  background: var(--gradient-surface);
  position: relative;
  overflow: hidden;
}

/* About Section 背景图片容器 */
.about-section .section-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  opacity: 0;
  transition: opacity 1s ease-out;
}

.about-section .section-background.loaded {
  opacity: 1;
}

.about-section .section-background img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* About Section 遮罩层 - 企业发展主题 */
.about-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.6) 0%,
    rgba(20, 20, 30, 0.7) 25%,
    rgba(40, 40, 50, 0.6) 50%,
    rgba(20, 20, 30, 0.7) 75%,
    rgba(0, 0, 0, 0.6) 100%
  );
  z-index: 2;
  pointer-events: none;
}

/* About Section 光效叠加层 */
.about-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 30%, rgba(48, 209, 88, 0.12) 0%, transparent 50%),
              radial-gradient(circle at 80% 70%, rgba(0, 122, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 60% 20%, rgba(255, 149, 0, 0.08) 0%, transparent 50%);
  z-index: 3;
  pointer-events: none;
}

/* About Section 内容 */
.about-section .container {
  position: relative;
  z-index: 10;
}

/* About Section 专用样式优化 */
.about-section .section-title {
  background: linear-gradient(90deg, #30D158 0%, #007AFF 50%, #FF9500 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 20px rgba(48, 209, 88, 0.3);
}

.about-section .section-subtitle {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Services Section */
.services-section {
  background: var(--color-background);
}

/* Technology Section */
.technology-section {
  background: var(--gradient-surface);
  position: relative;
  overflow: hidden;
}

/* 科技背景图片容器 */
.technology-section .section-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  opacity: 0;
  transition: opacity 1s ease-out;
}

.technology-section .section-background.loaded {
  opacity: 1;
}

.technology-section .section-background img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* 科技背景遮罩层 */
.technology-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 10, 40, 0.8) 25%,
    rgba(0, 30, 80, 0.7) 50%,
    rgba(0, 10, 40, 0.8) 75%,
    rgba(0, 0, 0, 0.7) 100%
  );
  z-index: 2;
  pointer-events: none;
}

/* 科技光效叠加层 */
.technology-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(0, 122, 255, 0.15) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(48, 209, 88, 0.15) 0%, transparent 50%),
              radial-gradient(circle at 50% 50%, rgba(88, 86, 214, 0.1) 0%, transparent 60%);
  z-index: 3;
  pointer-events: none;
}

.technology-content {
  position: relative;
  z-index: 10;
}

/* Technology Section 专用样式优化 */
.technology-section .section-title {
  background: linear-gradient(90deg, #00D4FF 0%, #30D158 50%, #007AFF 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.technology-section .section-subtitle {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Partnership Section */
.partnership-section {
  background: var(--color-background);
}

/* Contact Section */
.contact-section {
  background: var(--gradient-surface);
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --color-text-primary: #ffffff;
    --color-text-secondary: #cccccc;
    --color-border: #666666;
  }
}