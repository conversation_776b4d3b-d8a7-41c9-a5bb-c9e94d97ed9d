/* {{CHENGQI:
// Action: Added
// Timestamp: 2025-01-11 16:15:00 +08:00
// Reason: Per P2-UI-005 创建响应式样式文件，确保各设备良好适配
// Principle_Applied: SOLID - 样式模块化设计；DRY - 复用响应式断点和布局模式
// Optimization: 移动优先设计，渐进增强，优化触摸交互
// Architectural_Note (AR): 遵循Apple设计规范，确保一致的用户体验
// Documentation_Note (DW): 相关设计规范见 /project_document/architecture/final_architecture_v1.0.md
// }} */

/* ==========================================================================
   湖北银科官网响应式样式文件 - 移动优先设计
   ========================================================================== */

/* 基础响应式设置 */
html {
  font-size: 14px; /* 移动端基础字体大小 */
}

/* 导航栏响应式 - 提供两种风格选择 */

/* 方案一：极简透明风格（当前激活） */
.main-nav {
  padding: var(--spacing-sm) 0;
  /* 极简透明背景 */
  background: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(25px) saturate(180%) !important;
  -webkit-backdrop-filter: blur(25px) saturate(180%) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
  box-shadow: 0 1px 20px rgba(0, 0, 0, 0.1) !important;
}

.main-nav.scrolled {
  background: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(30px) saturate(200%) !important;
  -webkit-backdrop-filter: blur(30px) saturate(200%) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08) !important;
  box-shadow: 0 2px 25px rgba(0, 0, 0, 0.15) !important;
}

/* 方案二：优化渐变风格（备选，需要注释掉方案一并取消注释此部分）
.main-nav {
  padding: var(--spacing-sm) 0;
  background: linear-gradient(90deg, rgba(0, 5, 20, 0.25) 0%, rgba(0, 15, 40, 0.25) 100%) !important;
  backdrop-filter: blur(20px) saturate(150%) !important;
  -webkit-backdrop-filter: blur(20px) saturate(150%) !important;
  border-bottom: 1px solid rgba(0, 122, 255, 0.03) !important;
  box-shadow: 0 1px 15px rgba(0, 10, 60, 0.08) !important;
}

.main-nav.scrolled {
  background: linear-gradient(90deg, rgba(0, 5, 20, 0.6) 0%, rgba(0, 15, 40, 0.6) 100%) !important;
  backdrop-filter: blur(25px) saturate(180%) !important;
  -webkit-backdrop-filter: blur(25px) saturate(180%) !important;
  border-bottom: 1px solid rgba(0, 122, 255, 0.06) !important;
  box-shadow: 0 2px 20px rgba(0, 10, 60, 0.12) !important;
}
*/

/* 方案三：完全无背景风格（最极简，需要注释掉方案一并取消注释此部分）
.main-nav {
  padding: var(--spacing-sm) 0;
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  border-bottom: none !important;
  box-shadow: none !important;
}

.main-nav.scrolled {
  background: rgba(0, 0, 0, 0.4) !important;
  backdrop-filter: blur(20px) saturate(150%) !important;
  -webkit-backdrop-filter: blur(20px) saturate(150%) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
  box-shadow: 0 1px 15px rgba(0, 0, 0, 0.1) !important;
}
*/

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.nav-menu {
  position: fixed;
  top: 0;
  right: -100%;
  width: 280px;
  height: 100vh;
  background: var(--nav-background-scrolled); /* 与导航栏一致的渐变背景 */
  backdrop-filter: blur(20px) saturate(150%);
  -webkit-backdrop-filter: blur(20px) saturate(150%);
  border-left: 1px solid var(--nav-border-scrolled);
  box-shadow: -5px 0 30px rgba(0, 10, 60, 0.3);
  display: flex;
  flex-direction: column;
  padding: var(--spacing-4xl) var(--spacing-xl);
  transition: right 0.3s ease-out;
  z-index: var(--z-modal);
}

.nav-menu.active {
  right: 0;
}

.nav-menu li {
  margin-bottom: var(--spacing-lg);
}

.nav-link {
  display: block;
  font-size: var(--font-size-lg);
  padding: var(--spacing-sm) 0;
  /* 优化移动端导航链接样式 */
  transition: all 0.3s ease-out;
  border-radius: var(--border-radius-md);
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.08);
  color: var(--color-text-primary);
  transform: translateX(4px);
}

.nav-toggle {
  display: flex;
  flex-direction: column;
  gap: 4px;
  cursor: pointer;
  z-index: var(--z-modal);
}

.nav-toggle span {
  width: 24px;
  height: 2px;
  background: var(--color-text-primary);
  transition: all 0.3s ease-out;
}

.nav-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.nav-toggle.active span:nth-child(2) {
  opacity: 0;
}

.nav-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Hero区域响应式 */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  /* 调整padding，确保内容不被导航栏遮挡，但不产生顶部空隙 */
  padding: var(--spacing-4xl) var(--spacing-md) var(--spacing-4xl) var(--spacing-md);
  /* 确保hero区域从页面顶部开始 */
  margin-top: 0;
}

.hero-content {
  max-width: 100%;
  z-index: 10; /* 提高z-index确保文字在最上层 */
  position: relative; /* 确保z-index生效 */
  /* 移动端调整顶部间距，避免被导航栏遮挡 */
  margin-top: 64px; /* 移动端导航栏高度 */
}

/* 平板和桌面设备的hero内容调整 */
@media (min-width: 768px) {
  .hero-content {
    margin-top: 72px; /* 桌面端导航栏高度 */
  }
}

.hero-title {
  font-size: var(--font-size-4xl);
  line-height: 1.1;
  margin-bottom: var(--spacing-lg);
}

.hero-subtitle {
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-2xl);
  max-width: 90%;
  margin-left: auto;
  margin-right: auto;
  /* 优化副标题颜色，增强可读性 */
  color: var(--hero-subtitle-color) !important;
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
}

.hero-cta {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  align-items: center;
}

.cta-primary,
.cta-secondary {
  width: 100%;
  max-width: 280px;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-align: center;
  cursor: pointer;
}

.cta-primary {
  background: var(--gradient-primary);
  color: var(--color-text-primary);
  border: none;
  box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease-out;
}

.cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 122, 255, 0.4);
}

.cta-secondary {
  background: var(--gradient-button-secondary);
  color: var(--color-text-primary);
  border: 1px solid rgba(0, 122, 255, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px rgba(0, 122, 255, 0.1);
  transition: all 0.3s ease-out;
}

.cta-secondary:hover {
  background: var(--gradient-secondary);
  border-color: rgba(0, 122, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 122, 255, 0.2);
}

/* 内容区域响应式 */
.about-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.about-stats {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-lg);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--gradient-surface);
  border-radius: var(--border-radius-lg);
}

.stat-number {
  display: block;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-secondary);
}

.stat-unit {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

.stat-label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-top: var(--spacing-xs);
}

/* 服务卡片响应式 */
.services-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-lg);
}

.service-card {
  background: var(--gradient-surface);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  text-align: center;
  transition: all 0.3s ease-out;
}

/* 联系表单响应式 */
.contact-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.form-input,
.form-textarea {
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  background: var(--color-surface);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.contact-item h4 {
  color: var(--color-secondary);
  margin-bottom: var(--spacing-sm);
}

/* 页脚响应式 */
.footer-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  text-align: center;
}

.footer-section h4 {
  margin-bottom: var(--spacing-md);
  color: var(--color-secondary);
}

.footer-section ul {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

/* 平板设备 (768px+) */
@media (min-width: 768px) {
  html {
    font-size: 15px;
  }

  .nav-menu {
    position: static;
    width: auto;
    height: auto;
    background: transparent;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    box-shadow: none;
    border-left: none;
    flex-direction: row;
    padding: 0;
    gap: var(--spacing-xl);
  }

  .nav-menu li {
    margin-bottom: 0;
  }

  .nav-link {
    font-size: var(--font-size-base);
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .nav-toggle {
    display: none;
  }

  .hero-title {
    font-size: var(--font-size-5xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-lg);
    max-width: 600px;
  }

  .hero-cta {
    flex-direction: row;
    justify-content: center;
  }

  .about-content {
    flex-direction: row;
    align-items: center;
  }

  .about-timeline {
    flex: 1;
  }

  .about-stats {
    grid-template-columns: repeat(3, 1fr);
    max-width: 400px;
  }

  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .contact-content {
    flex-direction: row;
  }

  .contact-form-container {
    flex: 1;
  }

  .contact-info {
    flex: 1;
  }

  .footer-content {
    flex-direction: row;
    text-align: left;
    justify-content: space-between;
  }
}

/* 桌面设备 (1024px+) */
@media (min-width: 1024px) {
  html {
    font-size: 16px;
  }

  .nav-container {
    padding: 0 var(--spacing-2xl);
  }

  .hero-section {
    /* 桌面设备上保持合理的内边距，但确保顶部没有额外空隙 */
    padding: var(--spacing-5xl) var(--spacing-2xl) var(--spacing-5xl) var(--spacing-2xl);
    margin-top: 0;
  }

  .hero-title {
    font-size: var(--font-size-6xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-xl);
  }

  .services-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .about-stats {
    max-width: 500px;
  }
}

/* 大屏设备 (1280px+) */
@media (min-width: 1280px) {
  .container {
    max-width: 1200px;
  }

  .hero-title {
    font-size: 4rem;
  }

  .services-grid {
    gap: var(--spacing-xl);
  }
}

/* 超大屏设备 (1536px+) */
@media (min-width: 1536px) {
  .container {
    max-width: 1400px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .cta-primary,
  .cta-secondary,
  .nav-link,
  .service-card {
    min-height: 44px; /* 确保触摸目标足够大 */
  }

  .nav-link {
    padding: var(--spacing-md);
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .hero-canvas {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 横屏移动设备 */
@media (max-height: 500px) and (orientation: landscape) {
  .hero-section {
    min-height: auto;
    padding: var(--spacing-2xl) var(--spacing-md);
  }

  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-lg);
  }
}

/* 文字显示保障样式 - 确保在任何情况下文字都能正确显示 */
.hero-title,
.hero-subtitle {
  /* 强制显示文字，防止被遮挡 */
  opacity: 1 !important;
  visibility: visible !important;
  z-index: 20 !important;
  position: relative !important;
  /* 增强文字对比度 */
  text-shadow:
    0 1px 3px rgba(0, 0, 0, 0.8),
    0 2px 6px rgba(0, 0, 0, 0.6),
    0 3px 9px rgba(0, 0, 0, 0.4) !important;
  /* 确保文字颜色正确 */
  color: var(--color-text-primary) !important;
}

.hero-subtitle {
  color: var(--hero-subtitle-color) !important;
}

/* 确保Canvas不会遮挡文字 */
.hero-canvas {
  z-index: 1 !important;
  pointer-events: none; /* 防止Canvas阻止文字交互 */
}

/* 打印样式 */
@media print {
  .nav-toggle,
  .hero-canvas,
  .loading-screen {
    display: none !important;
  }

  .hero-section {
    min-height: auto;
    page-break-inside: avoid;
  }

  body {
    background: white !important;
    color: black !important;
  }
}
