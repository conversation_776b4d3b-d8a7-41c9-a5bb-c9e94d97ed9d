/* {{CHENGQI:
// Action: Added
// Timestamp: 2025-01-11 18:15:00 +08:00
// Reason: 为DataVisualization组件创建专用样式文件，修复科技创新部分布局问题
// Principle_Applied: SOLID - 样式模块化设计；DRY - 复用数据可视化样式模式
// Optimization: 使用CSS Grid和Flexbox优化布局，支持响应式设计
// Architectural_Note (AR): 配合DataVisualization.js实现数据展示和动画效果
// Documentation_Note (DW): 相关设计规范见 /project_document/architecture/final_architecture_v1.0.md
// }} */

/* ==========================================================================
   数据可视化组件样式 - Data Visualization
   ========================================================================== */

/* 科技可视化容器 */
.tech-visualization {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4xl);
  padding: var(--spacing-2xl) 0;
}

/* 数据统计网格 */
.data-stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

/* 数据统计卡片 */
.data-stat-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: var(--gradient-surface);
  border-radius: var(--border-radius-xl);
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease-out;
  opacity: 0;
  transform: translateY(30px) scale(0.95);
}

.data-stat-card--animated {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.data-stat-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-2xl);
  border-color: var(--accent-color, var(--color-secondary));
}

/* 统计图标 */
.stat-icon {
  font-size: 3rem;
  line-height: 1;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  flex-shrink: 0;
}

/* 统计内容 */
.stat-content {
  flex: 1;
}

.stat-value {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.stat-number {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--accent-color, var(--color-secondary));
  line-height: 1;
}

.stat-unit,
.stat-suffix {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

.stat-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1.3;
}

.stat-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: 1.5;
  margin: 0;
}

/* 技术栈部分 */
.tech-stack-section {
  margin-bottom: var(--spacing-2xl);
}

.tech-stack-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.tech-stack-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-lg);
}

/* 技术项目 */
.tech-item {
  padding: var(--spacing-lg);
  background: var(--color-surface);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--color-border);
  transition: all 0.3s ease-out;
  opacity: 0;
  transform: translateX(-30px);
}

.tech-item--animated {
  opacity: 1;
  transform: translateX(0);
}

.tech-item:hover {
  background: var(--gradient-surface);
  border-color: var(--tech-color, var(--color-secondary));
  transform: translateX(4px);
}

.tech-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.tech-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.tech-percentage {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--tech-color, var(--color-secondary));
}

/* 技术进度条 */
.tech-progress {
  height: 8px;
  background: var(--color-border);
  border-radius: 4px;
  overflow: hidden;
}

.tech-progress-bar {
  height: 100%;
  background: var(--tech-color, var(--color-secondary));
  border-radius: 4px;
  width: 0;
  transition: width 1.5s ease-out 0.3s;
}

.tech-item--animated .tech-progress-bar {
  width: var(--tech-level, 0%);
}

/* AI模型部分 */
.ai-model-section {
  margin-bottom: var(--spacing-2xl);
}

.ai-model-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.ai-model-diagram {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: var(--gradient-surface);
  border-radius: var(--border-radius-xl);
  border: 1px solid var(--color-border);
}

/* 模型层 */
.model-layer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--color-surface);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--color-border);
  min-width: 280px;
  opacity: 0;
  transform: scale(0.9);
  transition: all 0.8s ease-out;
}

.model-layer--animated {
  opacity: 1;
  transform: scale(1);
}

.model-layer h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
  text-align: center;
}

.layer-items {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-sm);
  width: 100%;
}

.layer-item {
  padding: var(--spacing-sm);
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  text-align: center;
  transition: all 0.3s ease-out;
  opacity: 0;
  transform: translateY(10px);
}

.layer-item--animated {
  opacity: 1;
  transform: translateY(0);
}

.layer-item:hover {
  background: var(--gradient-surface);
  color: var(--color-text-primary);
  border-color: var(--color-secondary);
}

/* 模型箭头 */
.model-arrow {
  color: var(--color-text-secondary);
  opacity: 0.7;
}

.model-arrow svg {
  display: block;
}

/* 不同层的颜色主题 */
.model-layer--input {
  border-color: #007AFF;
}

.model-layer--input h4 {
  color: #007AFF;
}

.model-layer--processing {
  border-color: #30D158;
}

.model-layer--processing h4 {
  color: #30D158;
}

.model-layer--output {
  border-color: #FF9500;
}

.model-layer--output h4 {
  color: #FF9500;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .data-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-2xl);
  }

  .tech-stack-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xl);
  }

  .ai-model-diagram {
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }

  .layer-items {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .data-stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .tech-stack-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .model-layer {
    min-width: 320px;
  }
}

@media (min-width: 1280px) {
  .tech-stack-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* 移动端优化 */
.tech-visualization--mobile .data-stats-grid {
  grid-template-columns: 1fr;
}

.tech-visualization--mobile .tech-stack-grid {
  grid-template-columns: 1fr;
}

.tech-visualization--mobile .ai-model-diagram {
  flex-direction: column;
  padding: var(--spacing-lg);
}

.tech-visualization--mobile .layer-items {
  grid-template-columns: 1fr;
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .data-stat-card,
  .tech-item,
  .model-layer,
  .layer-item {
    transition: opacity 0.3s ease-out;
    transform: none;
  }

  .data-stat-card--animated,
  .tech-item--animated,
  .model-layer--animated,
  .layer-item--animated {
    transform: none;
  }

  .tech-progress-bar {
    transition: width 0.5s ease-out;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .data-stat-card,
  .tech-item,
  .model-layer {
    border-width: 2px;
  }
}

/* 打印样式 */
@media print {
  .tech-visualization {
    gap: var(--spacing-lg);
  }

  .data-stat-card,
  .tech-item,
  .model-layer {
    opacity: 1;
    transform: none;
    box-shadow: none;
    border: 1px solid #ccc;
    page-break-inside: avoid;
  }
}
