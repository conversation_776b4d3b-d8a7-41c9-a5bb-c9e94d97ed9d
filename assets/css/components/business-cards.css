/* {{CHENGQI:
// Action: Added
// Timestamp: 2025-01-11 17:15:00 +08:00
// Reason: 为BusinessCards组件创建专用样式文件
// Principle_Applied: SOLID - 样式模块化设计；DRY - 复用卡片样式和动画效果
// Optimization: 使用CSS 3D变换实现卡片翻转效果，硬件加速优化性能
// Architectural_Note (AR): 配合BusinessCards.js实现流畅的卡片交互和动画效果
// Documentation_Note (DW): 相关设计规范见 /project_document/architecture/final_architecture_v1.0.md
// }} */

/* ==========================================================================
   业务卡片组件样式 - Business Cards
   ========================================================================== */

/* 业务卡片网格 */
.services-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-xl);
  padding: var(--spacing-lg) 0;
}

/* 业务卡片 */
.business-card {
  position: relative;
  height: 400px;
  perspective: 1000px;
  opacity: 0;
  transform: translateY(30px) scale(0.95);
  transition: all 0.8s ease-out;
}

.business-card--animated {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* 卡片内部容器 */
.business-card__inner {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  transition: transform 0.6s ease-out;
}

.business-card--flipped .business-card__inner {
  transform: rotateY(180deg);
}

/* 卡片正面和背面共同样式 */
.business-card__front,
.business-card__back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  background: var(--gradient-surface);
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease-out;
}

/* 卡片正面 */
.business-card__front {
  transform: rotateY(0deg);
}

/* 卡片背面 */
.business-card__back {
  transform: rotateY(180deg);
  background: var(--color-surface);
}

/* 卡片悬停效果 */
.business-card:hover .business-card__front,
.business-card:hover .business-card__back {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

/* 卡片头部 */
.business-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
}

.business-card__icon {
  font-size: 3rem;
  line-height: 1;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.business-card__stats {
  text-align: right;
}

.stats-value {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-secondary);
  line-height: 1;
}

.stats-label {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin-top: var(--spacing-xs);
}

/* 卡片内容 */
.business-card__content {
  flex: 1;
  margin-bottom: var(--spacing-lg);
}

.business-card__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
  line-height: 1.3;
}

.business-card__subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-secondary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-md);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.business-card__description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* 卡片底部 */
.business-card__footer {
  display: flex;
  justify-content: center;
}

.business-card__flip-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  background: rgba(0, 122, 255, 0.1);
  color: var(--color-secondary);
  border: 1px solid rgba(0, 122, 255, 0.3);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.3s ease-out;
}

.business-card__flip-btn:hover {
  background: rgba(0, 122, 255, 0.2);
  border-color: var(--color-secondary);
  transform: translateY(-2px);
}

.business-card__flip-btn svg {
  transition: transform 0.3s ease-out;
}

.business-card__flip-btn:hover svg {
  transform: rotate(180deg);
}

/* 卡片背面样式 */
.business-card__back-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}

.business-card__back-header h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
}

.business-card__close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.3s ease-out;
}

.business-card__close-btn:hover {
  background: var(--color-border);
  color: var(--color-text-primary);
}

/* 功能特色列表 */
.business-card__features {
  flex: 1;
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-item svg {
  flex-shrink: 0;
  color: var(--color-accent);
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}

/* 卡片背面底部 */
.business-card__back-footer {
  margin-top: var(--spacing-lg);
}

.business-card__contact-btn {
  width: 100%;
  padding: var(--spacing-md);
  background: var(--gradient-primary);
  color: var(--color-text-primary);
  border: none;
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.3s ease-out;
}

.business-card__contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3);
}

/* 不同颜色主题的卡片 */
.business-card--blue .business-card__icon {
  color: #007AFF;
}

.business-card--green .business-card__icon {
  color: #30D158;
}

.business-card--orange .business-card__icon {
  color: #FF9500;
}

.business-card--purple .business-card__icon {
  color: #AF52DE;
}

.business-card--blue .business-card__flip-btn {
  background: rgba(0, 122, 255, 0.1);
  border-color: rgba(0, 122, 255, 0.3);
  color: #007AFF;
}

.business-card--green .business-card__flip-btn {
  background: rgba(48, 209, 88, 0.1);
  border-color: rgba(48, 209, 88, 0.3);
  color: #30D158;
}

.business-card--orange .business-card__flip-btn {
  background: rgba(255, 149, 0, 0.1);
  border-color: rgba(255, 149, 0, 0.3);
  color: #FF9500;
}

.business-card--purple .business-card__flip-btn {
  background: rgba(175, 82, 222, 0.1);
  border-color: rgba(175, 82, 222, 0.3);
  color: #AF52DE;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-2xl);
  }
}

@media (min-width: 1024px) {
  .services-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .business-card {
    height: 450px;
  }

  .business-card__title {
    font-size: var(--font-size-lg);
  }
}

/* 移动端优化 */
@media (max-width: 767px) {
  .business-card {
    height: 350px;
  }

  .business-card__inner {
    padding: var(--spacing-lg);
  }

  .business-card__icon {
    font-size: 2.5rem;
  }

  .business-card__title {
    font-size: var(--font-size-lg);
  }

  /* 移动端禁用翻转效果 */
  .business-card--flipped .business-card__inner {
    transform: none;
  }

  .business-card__flip-btn {
    display: none;
  }

  .business-card__back {
    position: static;
    transform: none;
    margin-top: var(--spacing-lg);
    height: auto;
    background: var(--gradient-surface);
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .business-card__flip-btn,
  .business-card__contact-btn,
  .business-card__close-btn {
    min-height: 44px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .business-card {
    transition: opacity 0.3s ease-out;
    transform: none;
  }

  .business-card--animated {
    transform: none;
  }

  .business-card__inner {
    transition: none;
  }

  .business-card--flipped .business-card__inner {
    transform: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .business-card__front,
  .business-card__back {
    border-width: 2px;
  }

  .business-card__flip-btn,
  .business-card__close-btn {
    border-width: 2px;
  }
}

/* 打印样式 */
@media print {
  .business-card {
    opacity: 1;
    transform: none;
    height: auto;
    page-break-inside: avoid;
    margin-bottom: var(--spacing-lg);
  }

  .business-card__inner {
    transform: none;
  }

  .business-card__front,
  .business-card__back {
    position: static;
    transform: none;
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .business-card__flip-btn,
  .business-card__close-btn,
  .business-card__contact-btn {
    display: none;
  }
}
