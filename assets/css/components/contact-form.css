/* {{CHENGQI:
// Action: Added
// Timestamp: 2025-01-11 18:30:00 +08:00
// Reason: 为ContactForm组件创建专用样式文件
// Principle_Applied: SOLID - 样式模块化设计；DRY - 复用表单样式模式
// Optimization: 优化表单交互体验，提供清晰的视觉反馈
// Architectural_Note (AR): 配合ContactForm.js实现完整的表单功能
// Documentation_Note (DW): 相关设计规范见 /project_document/architecture/final_architecture_v1.0.md
// }} */

/* ==========================================================================
   联系表单组件样式 - Contact Form
   ========================================================================== */

/* 联系内容布局 */
.contact-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
  max-width: 1200px;
  margin: 0 auto;
}

.contact-form-container {
  flex: 1;
}

.contact-info {
  flex: 1;
}

/* 表单样式 */
.contact-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: var(--gradient-surface);
  border-radius: var(--border-radius-xl);
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-lg);
}

/* 表单组 */
.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

/* 表单标签 */
.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

/* 表单输入框 */
.form-input,
.form-select,
.form-textarea {
  padding: var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  background: var(--color-surface);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-family: inherit;
  transition: all 0.3s ease-out;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-secondary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
  background: var(--color-background);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--color-text-secondary);
  opacity: 0.7;
}

/* 错误状态 */
.form-input--error,
.form-select--error,
.form-textarea--error {
  border-color: var(--color-error, #FF3B30);
  background: rgba(255, 59, 48, 0.05);
}

.form-input--error:focus,
.form-select--error:focus,
.form-textarea--error:focus {
  box-shadow: 0 0 0 3px rgba(255, 59, 48, 0.1);
}

/* 文本域 */
.form-textarea {
  min-height: 120px;
  resize: vertical;
  line-height: 1.6;
}

/* 选择框 */
.form-select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}

/* 表单错误信息 */
.form-error {
  display: none;
  font-size: var(--font-size-xs);
  color: var(--color-error, #FF3B30);
  margin-top: var(--spacing-xs);
  padding-left: var(--spacing-sm);
}

.form-error:before {
  content: '⚠ ';
  margin-right: var(--spacing-xs);
}

/* 提交按钮 */
.form-submit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  background: var(--gradient-primary);
  color: var(--color-text-primary);
  border: none;
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.3s ease-out;
  min-height: 48px;
}

.form-submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3);
}

.form-submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* 加载状态 */
.submit-loading {
  display: none;
  align-items: center;
  gap: var(--spacing-sm);
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 表单消息 */
.form-message {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  display: none;
}

.form-message--success {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: rgba(48, 209, 88, 0.1);
  color: var(--color-accent, #30D158);
  border: 1px solid rgba(48, 209, 88, 0.3);
}

.form-message--error {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: rgba(255, 59, 48, 0.1);
  color: var(--color-error, #FF3B30);
  border: 1px solid rgba(255, 59, 48, 0.3);
}

/* 隐私声明 */
.form-privacy {
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-border);
}

.form-privacy p {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  line-height: 1.5;
  margin: 0;
}

/* 联系信息 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.contact-item {
  padding: var(--spacing-lg);
  background: var(--color-surface);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--color-border);
  transition: all 0.3s ease-out;
}

.contact-item:hover {
  background: var(--gradient-surface);
  border-color: var(--color-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.contact-item h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-secondary);
  margin-bottom: var(--spacing-sm);
}

.contact-item p {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .contact-content {
    flex-direction: row;
    align-items: flex-start;
  }

  .contact-form-container {
    flex: 2;
  }

  .contact-info {
    flex: 1;
    max-width: 400px;
  }
}

@media (min-width: 1024px) {
  .contact-form {
    padding: var(--spacing-2xl);
  }
}

/* 移动端优化 */
@media (max-width: 767px) {
  .contact-form {
    padding: var(--spacing-lg);
  }

  .form-input,
  .form-select,
  .form-textarea,
  .form-submit-btn {
    font-size: 16px; /* 防止iOS缩放 */
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .form-input,
  .form-select,
  .form-textarea,
  .form-submit-btn {
    min-height: 44px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .form-input,
  .form-select,
  .form-textarea {
    border-width: 2px;
  }

  .form-input--error,
  .form-select--error,
  .form-textarea--error {
    border-width: 3px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .form-input,
  .form-select,
  .form-textarea,
  .form-submit-btn,
  .contact-item {
    transition: none;
  }

  .form-submit-btn:hover:not(:disabled) {
    transform: none;
  }

  .contact-item:hover {
    transform: none;
  }

  .loading-spinner {
    animation: none;
  }
}

/* 打印样式 */
@media print {
  .contact-form {
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .form-submit-btn {
    display: none;
  }

  .form-message {
    display: none;
  }
}
