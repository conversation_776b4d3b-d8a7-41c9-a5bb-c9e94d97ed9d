/* {{CHENGQI:
// Action: Added
// Timestamp: 2025-01-11 16:45:00 +08:00
// Reason: 为TimelineSection组件创建专用样式文件
// Principle_Applied: SOLID - 样式模块化设计；DRY - 复用时间轴样式模式
// Optimization: 使用CSS变量和transform优化动画性能
// Architectural_Note (AR): 配合TimelineSection.js实现流畅的时间轴动画效果
// Documentation_Note (DW): 相关设计规范见 /project_document/architecture/final_architecture_v1.0.md
// }} */

/* ==========================================================================
   时间轴组件样式 - Timeline Section
   ========================================================================== */

/* 时间轴容器 */
.timeline-container {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--spacing-2xl) 0;
}

/* 时间轴主线 */
.timeline-line {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--color-border);
  transform: translateX(-50%);
  z-index: 1;
}

.timeline-line::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: var(--timeline-progress, 0%);
  background: var(--gradient-primary);
  transition: height 0.8s ease-out;
}

/* 时间轴项目 */
.timeline-item {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-3xl);
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

.timeline-item--animated {
  opacity: 1;
  transform: translateY(0);
}

/* 左侧项目 */
.timeline-item--left {
  flex-direction: row;
  text-align: right;
}

.timeline-item--left .timeline-content {
  margin-right: var(--spacing-xl);
  padding-right: var(--spacing-lg);
}

/* 右侧项目 */
.timeline-item--right {
  flex-direction: row-reverse;
  text-align: left;
}

.timeline-item--right .timeline-content {
  margin-left: var(--spacing-xl);
  padding-left: var(--spacing-lg);
}

/* 时间轴标记 */
.timeline-marker {
  position: relative;
  z-index: 2;
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gradient-surface);
  border: 3px solid var(--color-secondary);
  border-radius: 50%;
  transition: all 0.3s ease-out;
}

.timeline-item--animated .timeline-marker {
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(0, 122, 255, 0.4);
}

.timeline-icon {
  font-size: var(--font-size-xl);
  line-height: 1;
}

/* 时间轴内容 */
.timeline-content {
  flex: 1;
  max-width: 400px;
  background: var(--gradient-surface);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease-out;
}

.timeline-item:hover .timeline-content {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

/* 时间日期 */
.timeline-date {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.timeline-year {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-secondary);
  background: rgba(0, 122, 255, 0.1);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-md);
}

.timeline-month {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  background: var(--color-surface);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
}

/* 时间轴标题 */
.timeline-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
  line-height: 1.3;
}

/* 时间轴描述 */
.timeline-description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* 不同类型的时间轴项目样式 */
.timeline-item--milestone .timeline-marker {
  border-color: var(--color-secondary);
  background: var(--gradient-primary);
}

.timeline-item--milestone .timeline-icon {
  color: var(--color-text-primary);
}

.timeline-item--achievement .timeline-marker {
  border-color: var(--color-accent);
  background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-secondary) 100%);
}

.timeline-item--achievement .timeline-icon {
  color: var(--color-text-primary);
}

.timeline-item--period .timeline-marker {
  border-color: var(--color-text-secondary);
  background: var(--color-surface);
}

.timeline-item--period .timeline-icon {
  color: var(--color-text-secondary);
}

.timeline-item--current .timeline-marker {
  border-color: var(--color-accent);
  background: var(--color-accent);
  animation: pulse 2s ease-in-out infinite;
}

.timeline-item--current .timeline-icon {
  color: var(--color-background);
}

.timeline-item--current .timeline-content {
  border: 1px solid var(--color-accent);
  box-shadow: 0 0 20px rgba(48, 209, 88, 0.2);
}

/* 移动端适配 */
@media (max-width: 767px) {
  .timeline-line {
    left: 30px;
  }

  .timeline-item {
    flex-direction: row !important;
    text-align: left !important;
  }

  .timeline-item--left,
  .timeline-item--right {
    flex-direction: row;
    text-align: left;
  }

  .timeline-item--left .timeline-content,
  .timeline-item--right .timeline-content {
    margin-left: var(--spacing-lg);
    margin-right: 0;
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
  }

  .timeline-marker {
    width: 50px;
    height: 50px;
    margin-left: 5px;
  }

  .timeline-icon {
    font-size: var(--font-size-lg);
  }

  .timeline-content {
    max-width: none;
    padding: var(--spacing-lg);
  }

  .timeline-title {
    font-size: var(--font-size-lg);
  }

  .timeline-description {
    font-size: var(--font-size-sm);
  }
}

/* 平板端适配 */
@media (min-width: 768px) and (max-width: 1023px) {
  .timeline-content {
    max-width: 300px;
    padding: var(--spacing-lg);
  }

  .timeline-title {
    font-size: var(--font-size-lg);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .timeline-marker {
    border-width: 4px;
  }

  .timeline-content {
    border: 1px solid var(--color-border);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .timeline-item {
    transition: opacity 0.3s ease-out;
    transform: none;
  }

  .timeline-item--animated {
    transform: none;
  }

  .timeline-line::before {
    transition: height 0.3s ease-out;
  }

  .timeline-marker {
    transition: none;
  }

  .timeline-item--animated .timeline-marker {
    transform: none;
    animation: none;
  }

  .timeline-item--current .timeline-marker {
    animation: none;
  }
}

/* 打印样式 */
@media print {
  .timeline-container {
    max-width: none;
  }

  .timeline-line {
    display: none;
  }

  .timeline-item {
    opacity: 1;
    transform: none;
    margin-bottom: var(--spacing-lg);
    page-break-inside: avoid;
  }

  .timeline-content {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}
