/**
 * ScrollAnimationEngine.js
 * 滚动动画引擎，负责处理滚动事件与动画同步
 * 
 * {{CHENGQI:
 * Action: Added
 * Timestamp: 2025-01-11 09:30:00 +08:00
 * Reason: Per P1-LD-002 创建核心动画引擎的滚动动画组件
 * Principle_Applied: SOLID - 单一职责原则，仅负责滚动事件处理和进度计算；DRY - 提供通用的滚动动画逻辑，避免重复代码
 * Optimization: 使用requestAnimationFrame和节流技术优化滚动事件处理，保证60fps流畅度
 * Architectural_Note (AR): 作为动画引擎的核心组件，为图片序列播放器提供滚动进度数据
 * Documentation_Note (DW): 相关架构文档见 /project_document/architecture/final_architecture_v1.0.md
 * }}
 */

class ScrollAnimationEngine {
    /**
     * 构造函数
     * @param {Object} options 配置选项
     */
    constructor(options = {}) {
        // 默认配置
        this.config = {
            // 目标元素（滚动容器）
            container: options.container || window,
            // 动画触发元素
            triggerElement: options.triggerElement || document.body,
            // 动画开始位置 (0-1，相对于视口顶部)
            startPosition: options.startPosition !== undefined ? options.startPosition : 0,
            // 动画结束位置 (0-1，相对于视口底部)
            endPosition: options.endPosition !== undefined ? options.endPosition : 1,
            // 是否反向（向上滚动时动画前进）
            isReversed: options.isReversed || false,
            // 动画进度回调
            onProgressChange: options.onProgressChange || null,
            // 动画进入可视区域回调
            onEnterView: options.onEnterView || null,
            // 动画离开可视区域回调
            onLeaveView: options.onLeaveView || null,
            // 滚动事件节流时间(ms)
            throttleTime: options.throttleTime || 10,
            // 是否自动绑定滚动事件
            autoBindEvents: options.autoBindEvents !== undefined ? options.autoBindEvents : true,
            // 是否启用调试模式
            debug: options.debug || false
        };

        // 状态变量
        this.state = {
            // 当前动画进度 (0-1)
            progress: 0,
            // 元素是否在视口内
            isInView: false,
            // 上一次滚动位置
            lastScrollPosition: 0,
            // 滚动方向 ('up' 或 'down')
            scrollDirection: 'down',
            // 触发元素的边界信息
            triggerBounds: null
        };

        // 私有变量
        this._ticking = false;
        this._rafId = null;
        this._boundScrollHandler = this._handleScroll.bind(this);
        this._boundResizeHandler = this._handleResize.bind(this);
        this._lastThrottleTime = 0;

        // 如果配置为自动绑定事件，则初始化时立即绑定
        if (this.config.autoBindEvents) {
            this.bindScrollEvents();
        }
    }

    /**
     * 绑定滚动和调整大小事件
     * @returns {ScrollAnimationEngine} 当前实例，支持链式调用
     */
    bindScrollEvents() {
        // 绑定滚动事件
        this.config.container.addEventListener('scroll', this._boundScrollHandler, { passive: true });
        
        // 绑定窗口调整大小事件
        window.addEventListener('resize', this._boundResizeHandler);
        
        // 初始化触发元素边界
        this._updateTriggerBounds();
        
        // 初始计算一次进度
        this._handleScroll();
        
        return this;
    }

    /**
     * 解除事件绑定
     * @returns {ScrollAnimationEngine} 当前实例，支持链式调用
     */
    destroy() {
        // 解除滚动事件绑定
        this.config.container.removeEventListener('scroll', this._boundScrollHandler);
        
        // 解除窗口调整大小事件绑定
        window.removeEventListener('resize', this._boundResizeHandler);
        
        // 取消任何待处理的动画帧
        if (this._rafId) {
            cancelAnimationFrame(this._rafId);
            this._rafId = null;
        }
        
        return this;
    }

    /**
     * 计算滚动进度
     * @param {number} scrollPosition 当前滚动位置
     * @returns {number} 计算出的进度值 (0-1)
     */
    calculateProgress(scrollPosition) {
        // 如果触发元素边界未初始化，返回0
        if (!this.state.triggerBounds) {
            return 0;
        }
        
        const { top, height } = this.state.triggerBounds;
        const windowHeight = window.innerHeight;
        
        // 计算开始和结束的滚动位置
        const startScrollPosition = top - windowHeight + (height * this.config.startPosition);
        const endScrollPosition = top - windowHeight + (height * this.config.endPosition);
        const scrollRange = endScrollPosition - startScrollPosition;
        
        // 如果滚动范围为0，避免除以0错误
        if (scrollRange === 0) {
            return 0;
        }
        
        // 计算原始进度
        let progress = (scrollPosition - startScrollPosition) / scrollRange;
        
        // 限制进度在0-1范围内
        progress = Math.max(0, Math.min(1, progress));
        
        // 如果配置为反向，则反转进度
        if (this.config.isReversed) {
            progress = 1 - progress;
        }
        
        return progress;
    }

    /**
     * 更新动画状态
     * @param {number} progress 动画进度 (0-1)
     * @returns {ScrollAnimationEngine} 当前实例，支持链式调用
     */
    updateAnimation(progress) {
        // 更新进度状态
        this.state.progress = progress;
        
        // 调用进度变化回调
        if (typeof this.config.onProgressChange === 'function') {
            this.config.onProgressChange(progress);
        }
        
        // 检查元素是否进入/离开视口
        const wasInView = this.state.isInView;
        this.state.isInView = progress > 0 && progress < 1;
        
        // 如果状态发生变化，调用相应回调
        if (!wasInView && this.state.isInView && typeof this.config.onEnterView === 'function') {
            this.config.onEnterView();
        } else if (wasInView && !this.state.isInView && typeof this.config.onLeaveView === 'function') {
            this.config.onLeaveView();
        }
        
        // 调试模式下输出信息
        if (this.config.debug) {
            console.log(`ScrollAnimationEngine: Progress = ${progress.toFixed(4)}, InView = ${this.state.isInView}`);
        }
        
        return this;
    }

    /**
     * 手动设置进度（用于外部控制）
     * @param {number} progress 要设置的进度值 (0-1)
     * @returns {ScrollAnimationEngine} 当前实例，支持链式调用
     */
    setProgress(progress) {
        const clampedProgress = Math.max(0, Math.min(1, progress));
        this.updateAnimation(clampedProgress);
        return this;
    }

    /**
     * 获取当前进度
     * @returns {number} 当前进度值 (0-1)
     */
    getProgress() {
        return this.state.progress;
    }

    /**
     * 更新触发元素的边界信息
     * @private
     */
    _updateTriggerBounds() {
        const element = this.config.triggerElement;
        if (element) {
            const rect = element.getBoundingClientRect();
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            this.state.triggerBounds = {
                top: rect.top + scrollTop,
                height: rect.height
            };
        }
    }

    /**
     * 处理滚动事件
     * @private
     */
    _handleScroll() {
        // 获取当前时间
        const now = Date.now();
        
        // 如果距离上次处理的时间小于节流时间，使用requestAnimationFrame延迟处理
        if (now - this._lastThrottleTime < this.config.throttleTime) {
            if (!this._ticking) {
                this._ticking = true;
                this._rafId = requestAnimationFrame(() => {
                    this._processScroll();
                    this._ticking = false;
                });
            }
            return;
        }
        
        // 更新上次处理时间
        this._lastThrottleTime = now;
        
        // 处理滚动
        this._processScroll();
    }

    /**
     * 处理实际的滚动逻辑
     * @private
     */
    _processScroll() {
        // 获取当前滚动位置
        const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
        
        // 确定滚动方向
        this.state.scrollDirection = scrollPosition > this.state.lastScrollPosition ? 'down' : 'up';
        this.state.lastScrollPosition = scrollPosition;
        
        // 计算进度并更新动画
        const progress = this.calculateProgress(scrollPosition);
        this.updateAnimation(progress);
    }

    /**
     * 处理窗口大小调整事件
     * @private
     */
    _handleResize() {
        // 使用requestAnimationFrame优化性能
        if (!this._ticking) {
            this._ticking = true;
            this._rafId = requestAnimationFrame(() => {
                // 更新触发元素边界
                this._updateTriggerBounds();
                
                // 重新计算进度
                this._processScroll();
                
                this._ticking = false;
            });
        }
    }
}

// 导出为全局变量和模块
if (typeof window !== 'undefined') {
    window.ScrollAnimationEngine = ScrollAnimationEngine;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = ScrollAnimationEngine;
} 