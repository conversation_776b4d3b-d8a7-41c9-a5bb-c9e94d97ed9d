/**
 * ResourceManager.js
 * 资源管理器模块，负责预加载和管理网站所需的各种资源（图片、视频、字体等）
 * 
 * {{CHENGQI:
 * Action: Added
 * Timestamp: 2025-01-10 14:45:00 +08:00
 * Reason: Per P1-AR-002 创建核心动画引擎的资源管理组件
 * Principle_Applied: SOLID - 单一职责原则，仅负责资源加载和管理；DRY - 统一资源加载逻辑，避免重复代码
 * Optimization: 实现资源分组和优先级加载，优化首屏加载体验
 * Architectural_Note (AR): 作为动画引擎的关键组件，确保图片序列和其他资源高效加载
 * Documentation_Note (DW): 相关架构文档见 /project_document/architecture/final_architecture_v1.0.md
 * }}
 */

class ResourceManager {
    constructor(options = {}) {
        // 默认配置
        this.config = {
            // 基础资源路径
            basePath: options.basePath || '',
            // 资源加载超时时间(ms)
            timeout: options.timeout || 30000,
            // 并发加载数量
            concurrency: options.concurrency || 6,
            // 重试次数
            retries: options.retries || 2,
            // 加载进度回调
            onProgress: options.onProgress || null,
            // 加载完成回调
            onComplete: options.onComplete || null,
            // 加载错误回调
            onError: options.onError || null,
            // 是否在控制台显示调试信息
            debug: options.debug || false
        };

        // 资源缓存
        this.cache = {
            image: new Map(),
            video: new Map(),
            json: new Map(),
            font: new Map(),
            audio: new Map(),
            other: new Map()
        };

        // 资源加载队列
        this._queue = [];
        // 当前正在加载的资源数量
        this._loading = 0;
        // 已加载资源数量
        this._loaded = 0;
        // 总资源数量
        this._total = 0;
        // 加载失败的资源
        this._failed = [];
        // 加载状态
        this._isLoading = false;
    }

    /**
     * 添加单个资源到加载队列
     * @param {string} url 资源URL
     * @param {string} type 资源类型: 'image', 'video', 'json', 'font', 'audio', 'other'
     * @param {string} id 资源ID，用于后续获取
     * @param {number} priority 加载优先级，数字越小优先级越高
     * @param {Object} options 额外选项
     * @returns {ResourceManager} 当前实例，支持链式调用
     */
    add(url, type = 'image', id = '', priority = 10, options = {}) {
        const resourceId = id || url.split('/').pop().split('.')[0];
        
        this._queue.push({
            url: this.config.basePath + url,
            type,
            id: resourceId,
            priority,
            options,
            status: 'pending',
            retries: 0
        });
        
        this._total++;
        
        return this;
    }

    /**
     * 批量添加资源到加载队列
     * @param {Array} resources 资源数组
     * @returns {ResourceManager} 当前实例，支持链式调用
     */
    addMultiple(resources) {
        if (!Array.isArray(resources)) {
            console.error('资源必须是数组格式');
            return this;
        }
        
        resources.forEach(resource => {
            this.add(
                resource.url,
                resource.type || 'image',
                resource.id || '',
                resource.priority || 10,
                resource.options || {}
            );
        });
        
        return this;
    }

    /**
     * 添加图片序列到加载队列
     * @param {string} baseUrl 基础URL
     * @param {string} format 文件格式，如'jpg', 'png', 'webp'
     * @param {number} start 起始索引
     * @param {number} end 结束索引
     * @param {string} prefix 文件名前缀
     * @param {string} suffix 文件名后缀
     * @param {number} digits 索引数字的位数，用于补零
     * @param {number} priority 加载优先级
     * @returns {ResourceManager} 当前实例，支持链式调用
     */
    addImageSequence(baseUrl, format, start, end, prefix = '', suffix = '', digits = 4, priority = 5) {
        for (let i = start; i <= end; i++) {
            const index = String(i).padStart(digits, '0');
            const filename = `${prefix}${index}${suffix}.${format}`;
            const id = `${prefix}${index}${suffix}`;
            
            this.add(`${baseUrl}/${filename}`, 'image', id, priority);
        }
        
        return this;
    }

    /**
     * 开始加载所有资源
     * @returns {Promise} 返回Promise，解析为加载结果
     */
    loadAll() {
        if (this._isLoading) {
            console.warn('资源加载器已在运行中');
            return;
        }
        
        this._isLoading = true;
        this._sortQueueByPriority();
        
        return new Promise((resolve, reject) => {
            if (this._queue.length === 0) {
                this._isLoading = false;
                this._handleComplete();
                resolve({
                    success: true,
                    loaded: 0,
                    failed: 0,
                    cache: this.cache
                });
                return;
            }
            
            // 设置超时
            const timeoutId = setTimeout(() => {
                if (this._loading > 0) {
                    console.error(`资源加载超时，${this._loading}个资源未完成加载`);
                    this._isLoading = false;
                    reject({
                        success: false,
                        error: '加载超时',
                        loaded: this._loaded,
                        failed: this._failed.length,
                        failedResources: this._failed
                    });
                }
            }, this.config.timeout);
            
            // 开始加载队列中的资源
            this._processQueue().then((result) => {
                clearTimeout(timeoutId);
                this._isLoading = false;
                resolve(result);
            }).catch((error) => {
                clearTimeout(timeoutId);
                this._isLoading = false;
                reject(error);
            });
        });
    }

    /**
     * 加载特定组的资源
     * @param {string|Array} groups 资源组名称或数组
     * @returns {Promise} 返回Promise，解析为加载结果
     */
    loadGroup(groups) {
        const targetGroups = Array.isArray(groups) ? groups : [groups];
        
        // 过滤队列中属于指定组的资源
        const groupQueue = this._queue.filter(item => 
            item.options.group && targetGroups.includes(item.options.group)
        );
        
        if (groupQueue.length === 0) {
            console.warn(`未找到属于组 ${targetGroups.join(', ')} 的资源`);
            return Promise.resolve({
                success: true,
                loaded: 0,
                failed: 0
            });
        }
        
        // 创建临时队列进行加载
        const tempManager = new ResourceManager(this.config);
        tempManager._queue = groupQueue;
        tempManager._total = groupQueue.length;
        
        return tempManager.loadAll();
    }

    /**
     * 获取已加载的资源
     * @param {string} id 资源ID
     * @param {string} type 资源类型
     * @returns {*} 资源对象
     */
    get(id, type = 'image') {
        if (!this.cache[type]) {
            console.error(`未知资源类型: ${type}`);
            return null;
        }
        
        const resource = this.cache[type].get(id);
        
        if (!resource) {
            console.warn(`未找到ID为 ${id} 的${type}资源`);
            return null;
        }
        
        return resource;
    }

    /**
     * 获取图片序列
     * @param {string} prefix 文件名前缀
     * @param {string} suffix 文件名后缀
     * @param {number} start 起始索引
     * @param {number} end 结束索引
     * @param {number} digits 索引数字的位数
     * @returns {Array} 图片对象数组
     */
    getImageSequence(prefix = '', suffix = '', start, end, digits = 4) {
        const sequence = [];
        
        for (let i = start; i <= end; i++) {
            const index = String(i).padStart(digits, '0');
            const id = `${prefix}${index}${suffix}`;
            const image = this.get(id, 'image');
            
            if (image) {
                sequence.push(image);
            } else {
                console.warn(`图片序列缺失: ${id}`);
            }
        }
        
        return sequence;
    }

    /**
     * 获取加载进度
     * @returns {number} 加载进度百分比(0-100)
     */
    getProgress() {
        if (this._total === 0) return 100;
        return Math.round((this._loaded / this._total) * 100);
    }

    /**
     * 清除所有资源缓存
     * @param {string} type 指定清除的资源类型，不指定则清除所有
     */
    clearCache(type) {
        if (type && this.cache[type]) {
            this.cache[type].clear();
        } else {
            Object.keys(this.cache).forEach(key => {
                this.cache[key].clear();
            });
        }
    }

    /**
     * 按优先级排序队列
     * @private
     */
    _sortQueueByPriority() {
        this._queue.sort((a, b) => a.priority - b.priority);
    }

    /**
     * 处理资源队列
     * @private
     * @returns {Promise} 返回Promise，解析为加载结果
     */
    _processQueue() {
        return new Promise((resolve) => {
            const loadNext = () => {
                // 如果队列为空或所有资源都已开始加载，则返回
                if (this._queue.length === 0 || this._loading >= this.config.concurrency) {
                    return;
                }
                
                // 获取下一个待加载的资源
                const nextResource = this._queue.find(item => item.status === 'pending');
                
                if (!nextResource) {
                    // 如果没有待加载的资源，检查是否所有资源都已加载完成
                    if (this._loading === 0) {
                        this._handleComplete();
                        resolve({
                            success: true,
                            loaded: this._loaded,
                            failed: this._failed.length,
                            failedResources: this._failed,
                            cache: this.cache
                        });
                    }
                    return;
                }
                
                // 更新资源状态为加载中
                nextResource.status = 'loading';
                this._loading++;
                
                // 加载资源
                this._loadResource(nextResource)
                    .then(() => {
                        // 加载成功
                        nextResource.status = 'loaded';
                        this._loaded++;
                        this._loading--;
                        
                        // 更新加载进度
                        this._updateProgress();
                        
                        // 继续加载下一个资源
                        loadNext();
                    })
                    .catch(() => {
                        // 加载失败
                        nextResource.status = 'failed';
                        nextResource.retries++;
                        
                        // 如果重试次数未达上限，则重新加入队列
                        if (nextResource.retries <= this.config.retries) {
                            nextResource.status = 'pending';
                            nextResource.priority += 1; // 降低优先级
                            this._sortQueueByPriority();
                        } else {
                            // 达到重试上限，标记为永久失败
                            this._failed.push({
                                id: nextResource.id,
                                url: nextResource.url,
                                type: nextResource.type
                            });
                        }
                        
                        this._loading--;
                        
                        // 更新加载进度
                        this._updateProgress();
                        
                        // 继续加载下一个资源
                        loadNext();
                    });
                
                // 如果并发数未达上限，继续加载下一个资源
                if (this._loading < this.config.concurrency) {
                    loadNext();
                }
            };
            
            // 开始加载资源
            for (let i = 0; i < this.config.concurrency; i++) {
                loadNext();
            }
        });
    }

    /**
     * 加载单个资源
     * @private
     * @param {Object} resource 资源对象
     * @returns {Promise} 返回Promise，解析为加载结果
     */
    _loadResource(resource) {
        return new Promise((resolve, reject) => {
            switch (resource.type) {
                case 'image':
                    this._loadImage(resource, resolve, reject);
                    break;
                case 'video':
                    this._loadVideo(resource, resolve, reject);
                    break;
                case 'json':
                    this._loadJSON(resource, resolve, reject);
                    break;
                case 'font':
                    this._loadFont(resource, resolve, reject);
                    break;
                case 'audio':
                    this._loadAudio(resource, resolve, reject);
                    break;
                default:
                    this._loadOther(resource, resolve, reject);
            }
        });
    }

    /**
     * 加载图片资源
     * @private
     * @param {Object} resource 资源对象
     * @param {Function} resolve Promise解析函数
     * @param {Function} reject Promise拒绝函数
     */
    _loadImage(resource, resolve, reject) {
        const img = new Image();
        
        img.onload = () => {
            this.cache.image.set(resource.id, img);
            if (this.config.debug) {
                console.log(`图片加载成功: ${resource.id}`);
            }
            resolve(img);
        };
        
        img.onerror = () => {
            console.error(`图片加载失败: ${resource.url}`);
            reject();
        };
        
        // 设置crossOrigin属性（如果需要）
        if (resource.options.crossOrigin) {
            img.crossOrigin = resource.options.crossOrigin;
        }
        
        img.src = resource.url;
    }

    /**
     * 加载视频资源
     * @private
     * @param {Object} resource 资源对象
     * @param {Function} resolve Promise解析函数
     * @param {Function} reject Promise拒绝函数
     */
    _loadVideo(resource, resolve, reject) {
        const video = document.createElement('video');
        
        video.addEventListener('loadeddata', () => {
            this.cache.video.set(resource.id, video);
            if (this.config.debug) {
                console.log(`视频加载成功: ${resource.id}`);
            }
            resolve(video);
        });
        
        video.addEventListener('error', () => {
            console.error(`视频加载失败: ${resource.url}`);
            reject();
        });
        
        // 设置视频属性
        if (resource.options.muted !== undefined) video.muted = resource.options.muted;
        if (resource.options.loop !== undefined) video.loop = resource.options.loop;
        if (resource.options.autoplay !== undefined) video.autoplay = resource.options.autoplay;
        if (resource.options.playsinline !== undefined) video.playsInline = resource.options.playsinline;
        
        // 设置crossOrigin属性（如果需要）
        if (resource.options.crossOrigin) {
            video.crossOrigin = resource.options.crossOrigin;
        }
        
        video.preload = 'auto';
        video.src = resource.url;
    }

    /**
     * 加载JSON资源
     * @private
     * @param {Object} resource 资源对象
     * @param {Function} resolve Promise解析函数
     * @param {Function} reject Promise拒绝函数
     */
    _loadJSON(resource, resolve, reject) {
        fetch(resource.url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                this.cache.json.set(resource.id, data);
                if (this.config.debug) {
                    console.log(`JSON加载成功: ${resource.id}`);
                }
                resolve(data);
            })
            .catch(error => {
                console.error(`JSON加载失败: ${resource.url}`, error);
                reject();
            });
    }

    /**
     * 加载字体资源
     * @private
     * @param {Object} resource 资源对象
     * @param {Function} resolve Promise解析函数
     * @param {Function} reject Promise拒绝函数
     */
    _loadFont(resource, resolve, reject) {
        const fontFace = new FontFace(
            resource.id,
            `url(${resource.url})`,
            resource.options
        );
        
        fontFace.load()
            .then(loadedFace => {
                document.fonts.add(loadedFace);
                this.cache.font.set(resource.id, loadedFace);
                if (this.config.debug) {
                    console.log(`字体加载成功: ${resource.id}`);
                }
                resolve(loadedFace);
            })
            .catch(error => {
                console.error(`字体加载失败: ${resource.url}`, error);
                reject();
            });
    }

    /**
     * 加载音频资源
     * @private
     * @param {Object} resource 资源对象
     * @param {Function} resolve Promise解析函数
     * @param {Function} reject Promise拒绝函数
     */
    _loadAudio(resource, resolve, reject) {
        const audio = new Audio();
        
        audio.addEventListener('canplaythrough', () => {
            this.cache.audio.set(resource.id, audio);
            if (this.config.debug) {
                console.log(`音频加载成功: ${resource.id}`);
            }
            resolve(audio);
        });
        
        audio.addEventListener('error', () => {
            console.error(`音频加载失败: ${resource.url}`);
            reject();
        });
        
        // 设置音频属性
        if (resource.options.preload) audio.preload = resource.options.preload;
        
        audio.src = resource.url;
        audio.load();
    }

    /**
     * 加载其他类型资源
     * @private
     * @param {Object} resource 资源对象
     * @param {Function} resolve Promise解析函数
     * @param {Function} reject Promise拒绝函数
     */
    _loadOther(resource, resolve, reject) {
        fetch(resource.url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.blob();
            })
            .then(blob => {
                this.cache.other.set(resource.id, blob);
                if (this.config.debug) {
                    console.log(`资源加载成功: ${resource.id}`);
                }
                resolve(blob);
            })
            .catch(error => {
                console.error(`资源加载失败: ${resource.url}`, error);
                reject();
            });
    }

    /**
     * 更新加载进度
     * @private
     */
    _updateProgress() {
        const progress = this.getProgress();
        
        // 调用进度回调
        if (typeof this.config.onProgress === 'function') {
            this.config.onProgress(progress, this._loaded, this._total);
        }
        
        // 更新加载屏幕上的进度条（如果存在）
        const progressElement = document.getElementById('progress-fill');
        if (progressElement) {
            progressElement.style.width = `${progress}%`;
        }
    }

    /**
     * 处理加载完成
     * @private
     */
    _handleComplete() {
        // 调用完成回调
        if (typeof this.config.onComplete === 'function') {
            this.config.onComplete({
                loaded: this._loaded,
                failed: this._failed.length,
                failedResources: this._failed,
                cache: this.cache
            });
        }
        
        // 如果所有资源加载成功，隐藏加载屏幕（如果存在）
        if (this._failed.length === 0) {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                setTimeout(() => {
                    loadingScreen.classList.add('loaded');
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 500);
                }, 500);
            }
        }
    }
}

// 导出为全局变量和模块
if (typeof window !== 'undefined') {
    window.ResourceManager = ResourceManager;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = ResourceManager;
} 