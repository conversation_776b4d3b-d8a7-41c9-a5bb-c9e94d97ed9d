/**
 * PerformanceDetector.js
 * 设备性能检测模块，用于评估当前设备性能并据此调整动画复杂度
 * 
 * {{CHENGQI:
 * Action: Added
 * Timestamp: 2025-01-10 14:15:00 +08:00
 * Reason: Per P1-AR-002 创建核心动画引擎的性能检测组件
 * Principle_Applied: SOLID - 单一职责原则，仅负责性能检测和性能级别判断
 * Optimization: 使用多维度指标综合评估设备性能，避免单一指标带来的误判
 * Architectural_Note (AR): 作为动画引擎的基础组件，为后续动画效果提供性能适配依据
 * Documentation_Note (DW): 相关架构文档见 /project_document/architecture/final_architecture_v1.0.md
 * }}
 * 
 * {{CHENGQI:
 * Action: Modified
 * Timestamp: 2025-01-10 16:25:00 +08:00
 * Reason: 修复runTest()方法返回undefined的问题，确保Promise正确返回性能级别
 * Principle_Applied: SOLID - 里氏替换原则，确保接口契约的可靠性；DRY - 避免重复测试逻辑
 * Optimization: 增加状态管理和Promise缓存，避免重复执行和竞争条件
 * Architectural_Note (AR): 修复后确保性能检测器的可靠性，为动画系统提供稳定的性能依据
 * Documentation_Note (DW): 问题分析和解决方案已记录在团队协作日志中
 * }}
 */

class PerformanceDetector {
    constructor(options = {}) {
        // 默认配置
        this.config = {
            // 性能测试时长(ms)
            testDuration: options.testDuration || 1000,
            // 性能级别阈值
            thresholds: options.thresholds || {
                high: { fps: 55, memory: 0.7, devicePixelRatio: 2 },
                medium: { fps: 40, memory: 0.5, devicePixelRatio: 1.5 }
                // 低于medium阈值则判定为low
            },
            // 是否自动运行性能检测
            autoRun: options.autoRun !== undefined ? options.autoRun : true,
            // 性能检测完成回调
            onComplete: options.onComplete || null
        };

        // 性能指标
        this.metrics = {
            fps: 0,               // 帧率
            memory: 0,            // 内存使用情况(如果可用)
            devicePixelRatio: window.devicePixelRatio || 1, // 设备像素比
            touchSupport: 'ontouchstart' in window,  // 触摸支持
            hardwareAcceleration: this._checkHardwareAcceleration(),
            connectionType: this._getConnectionType(),
            batteryStatus: null   // 电池状态(如果API可用)
        };

        // 性能级别: high, medium, low
        this.performanceLevel = 'medium';
        
        // 测试状态
        this._isRunning = false;
        this._testCompleted = false;
        this._frames = 0;
        this._startTime = 0;
        this._testPromise = null;
        
        // 如果配置为自动运行，则初始化时立即运行测试
        if (this.config.autoRun) {
            // 延迟执行，避免构造函数中的异步操作影响初始化
            setTimeout(() => {
                this.runTest().catch(console.error);
            }, 0);
        }
        
        // 尝试获取电池信息(如果API可用)
        this._getBatteryInfo();
    }
    
    /**
     * 运行性能测试
     * @returns {Promise} 返回一个Promise，解析为性能级别
     */
    runTest() {
        // 如果已经完成测试，直接返回结果
        if (this._testCompleted) {
            return Promise.resolve(this.performanceLevel);
        }
        
        // 如果正在运行测试，返回现有的Promise
        if (this._isRunning && this._testPromise) {
            return this._testPromise;
        }
        
        // 开始新的测试
        this._isRunning = true;
        this._frames = 0;
        this._startTime = performance.now();
        
        this._testPromise = new Promise((resolve) => {
            // 测试帧率
            this._testFrameRate();
            
            // 设置超时，在测试时长结束后计算性能
            setTimeout(() => {
                this._calculatePerformance();
                this._isRunning = false;
                this._testCompleted = true;
                
                if (typeof this.config.onComplete === 'function') {
                    this.config.onComplete(this.performanceLevel, this.metrics);
                }
                
                resolve(this.performanceLevel);
            }, this.config.testDuration);
        });
        
        return this._testPromise;
    }
    
    /**
     * 获取当前性能级别
     * @returns {string} 性能级别: 'high', 'medium', 'low'
     */
    getPerformanceLevel() {
        return this.performanceLevel;
    }
    
    /**
     * 获取详细性能指标
     * @returns {Object} 性能指标对象
     */
    getMetrics() {
        return { ...this.metrics };
    }
    
    /**
     * 重置性能检测器状态，以便重新运行测试
     */
    reset() {
        this._isRunning = false;
        this._testCompleted = false;
        this._testPromise = null;
        this._frames = 0;
        this.performanceLevel = 'medium'; // 重置为默认值
    }
    
    /**
     * 测试帧率
     * @private
     */
    _testFrameRate() {
        const loop = () => {
            if (!this._isRunning) return;
            
            this._frames++;
            requestAnimationFrame(loop);
        };
        
        requestAnimationFrame(loop);
    }
    
    /**
     * 计算综合性能并确定性能级别
     * @private
     */
    _calculatePerformance() {
        // 计算FPS
        const elapsed = performance.now() - this._startTime;
        this.metrics.fps = Math.round((this._frames / elapsed) * 1000);
        
        // 尝试获取内存信息(如果可用)
        if (performance.memory) {
            const memoryInfo = performance.memory;
            this.metrics.memory = memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit;
        }
        
        // 确定性能级别
        if (this._meetsThreshold('high')) {
            this.performanceLevel = 'high';
        } else if (this._meetsThreshold('medium')) {
            this.performanceLevel = 'medium';
        } else {
            this.performanceLevel = 'low';
        }
        
        // 记录性能检测结果到控制台(开发模式)
        // if (process.env.NODE_ENV !== 'production') {
        //     console.log('性能检测结果:', {
        //         level: this.performanceLevel,
        //         metrics: this.metrics
        //     });
        // }
    }
    
    /**
     * 检查是否满足指定性能级别的阈值
     * @param {string} level 性能级别
     * @returns {boolean} 是否满足阈值
     * @private
     */
    _meetsThreshold(level) {
        const threshold = this.config.thresholds[level];
        
        // 基本指标检查
        const fpsCheck = this.metrics.fps >= threshold.fps;
        const pixelRatioCheck = this.metrics.devicePixelRatio >= threshold.devicePixelRatio;
        
        // 内存检查(如果可用)
        let memoryCheck = true;
        if (this.metrics.memory > 0) {
            memoryCheck = this.metrics.memory >= threshold.memory;
        }
        
        // 综合评估
        return fpsCheck && pixelRatioCheck && memoryCheck;
    }
    
    /**
     * 检查硬件加速支持
     * @returns {boolean} 是否支持硬件加速
     * @private
     */
    _checkHardwareAcceleration() {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || 
                   canvas.getContext('experimental-webgl');
        
        return !!gl;
    }
    
    /**
     * 获取网络连接类型(如果API可用)
     * @returns {string} 连接类型
     * @private
     */
    _getConnectionType() {
        const connection = navigator.connection || 
                          navigator.mozConnection || 
                          navigator.webkitConnection;
                          
        return connection ? connection.effectiveType : 'unknown';
    }
    
    /**
     * 获取电池信息(如果API可用)
     * @private
     */
    _getBatteryInfo() {
        if ('getBattery' in navigator) {
            navigator.getBattery().then(battery => {
                this.metrics.batteryStatus = {
                    level: battery.level,
                    charging: battery.charging
                };
                
                // 监听电池状态变化
                battery.addEventListener('levelchange', () => {
                    this.metrics.batteryStatus.level = battery.level;
                });
                
                battery.addEventListener('chargingchange', () => {
                    this.metrics.batteryStatus.charging = battery.charging;
                });
            });
        }
    }
}

// 导出为全局变量和模块
if (typeof window !== 'undefined') {
    window.PerformanceDetector = PerformanceDetector;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceDetector;
} 