/**
 * ImageSequencePlayer.js
 * Canvas图片序列播放器，负责管理和渲染图片序列动画
 * 
 * {{CHENGQI:
 * Action: Added
 * Timestamp: 2025-01-11 10:15:00 +08:00
 * Reason: Per P1-LD-002 创建核心动画引擎的图片序列播放器组件
 * Principle_Applied: SOLID - 单一职责原则，仅负责图片序列渲染；开闭原则 - 设计为可扩展的播放器，支持多种播放模式
 * Optimization: 使用Canvas优化渲染性能，实现图片预缓存和内存管理
 * Architectural_Note (AR): 与ScrollAnimationEngine配合，实现Apple风格的滚动动画效果
 * Documentation_Note (DW): 相关架构文档见 /project_document/architecture/final_architecture_v1.0.md
 * }}
 */

class ImageSequencePlayer {
    /**
     * 构造函数
     * @param {HTMLCanvasElement|string} canvasElement Canvas元素或ID
     * @param {Array|null} imageUrls 图片URL数组（可选，也可后续通过setImages设置）
     * @param {Object} options 配置选项
     */
    constructor(canvasElement, imageUrls = null, options = {}) {
        // 获取Canvas元素
        this.canvas = typeof canvasElement === 'string' 
            ? document.getElementById(canvasElement) 
            : canvasElement;
            
        if (!this.canvas || !(this.canvas instanceof HTMLCanvasElement)) {
            throw new Error('无效的Canvas元素');
        }
        
        // 获取2D上下文
        this.ctx = this.canvas.getContext('2d');
        
        // 默认配置
        this.config = {
            // 自动调整Canvas尺寸以匹配容器
            autoSize: options.autoSize !== undefined ? options.autoSize : true,
            // 图片适应模式: 'contain', 'cover', 'stretch'
            fitMode: options.fitMode || 'contain',
            // 图片在Canvas中的对齐方式
            alignment: options.alignment || 'center',
            // 是否启用图片平滑
            smoothing: options.smoothing !== undefined ? options.smoothing : true,
            // 是否自动预加载图片
            autoPreload: options.autoPreload !== undefined ? options.autoPreload : true,
            // 加载进度回调
            onLoadProgress: options.onLoadProgress || null,
            // 加载完成回调
            onLoadComplete: options.onLoadComplete || null,
            // 帧渲染回调
            onFrameRender: options.onFrameRender || null,
            // 是否启用调试模式
            debug: options.debug || false
        };
        
        // 状态变量
        this.state = {
            // 图片数组
            images: [],
            // 已加载图片数量
            loadedCount: 0,
            // 当前帧索引
            currentFrame: 0,
            // 是否正在播放
            isPlaying: false,
            // 播放开始时间
            startTime: 0,
            // 播放持续时间(ms)
            duration: 0,
            // 开始帧
            startFrame: 0,
            // 结束帧
            endFrame: 0,
            // 播放方向 (1: 正向, -1: 反向)
            direction: 1,
            // 播放循环次数 (0: 无限循环)
            loopCount: 0,
            // 当前循环次数
            currentLoop: 0,
            // 是否已加载完成
            isLoaded: false
        };
        
        // 私有变量
        this._rafId = null;
        this._boundResizeHandler = this._handleResize.bind(this);
        
        // 初始化Canvas尺寸
        if (this.config.autoSize) {
            this._updateCanvasSize();
            window.addEventListener('resize', this._boundResizeHandler);
        }
        
        // 设置图片平滑
        this.ctx.imageSmoothingEnabled = this.config.smoothing;
        
        // 如果提供了图片URL，设置图片
        if (imageUrls && Array.isArray(imageUrls)) {
            this.setImages(imageUrls);
        }
    }
    
    /**
     * 设置图片序列
     * @param {Array} imageUrls 图片URL数组
     * @returns {ImageSequencePlayer} 当前实例，支持链式调用
     */
    setImages(imageUrls) {
        // 重置状态
        this.state.images = [];
        this.state.loadedCount = 0;
        this.state.isLoaded = false;
        
        // 如果没有图片，直接返回
        if (!imageUrls || !imageUrls.length) {
            return this;
        }
        
        // 初始化图片数组
        this.state.images = new Array(imageUrls.length);
        
        // 如果配置为自动预加载，则加载图片
        if (this.config.autoPreload) {
            this.preloadImages(imageUrls);
        }
        
        return this;
    }
    
    /**
     * 预加载图片
     * @param {Array} imageUrls 图片URL数组
     * @returns {Promise} 返回Promise，解析为加载结果
     */
    preloadImages(imageUrls = null) {
        // 如果没有提供图片URL，使用已设置的图片
        const urls = imageUrls || this.state.images.map(img => img.src);
        
        if (!urls || !urls.length) {
            return Promise.resolve({ success: true, loaded: 0, total: 0 });
        }
        
        // 重置加载状态
        this.state.loadedCount = 0;
        this.state.isLoaded = false;
        
        return new Promise((resolve) => {
            let loadedCount = 0;
            const totalCount = urls.length;
            
            // 加载每张图片
            urls.forEach((url, index) => {
                const img = new Image();
                
                img.onload = () => {
                    // 更新图片数组
                    this.state.images[index] = img;
                    loadedCount++;
                    this.state.loadedCount = loadedCount;
                    
                    // 调用进度回调
                    if (typeof this.config.onLoadProgress === 'function') {
                        const progress = loadedCount / totalCount;
                        this.config.onLoadProgress(progress, loadedCount, totalCount);
                    }
                    
                    // 如果所有图片都已加载，调用完成回调
                    if (loadedCount === totalCount) {
                        this.state.isLoaded = true;
                        
                        if (typeof this.config.onLoadComplete === 'function') {
                            this.config.onLoadComplete({
                                success: true,
                                loaded: loadedCount,
                                total: totalCount
                            });
                        }
                        
                        resolve({
                            success: true,
                            loaded: loadedCount,
                            total: totalCount
                        });
                    }
                };
                
                img.onerror = () => {
                    console.error(`图片加载失败: ${url}`);
                    loadedCount++;
                    
                    // 调用进度回调
                    if (typeof this.config.onLoadProgress === 'function') {
                        const progress = loadedCount / totalCount;
                        this.config.onLoadProgress(progress, loadedCount, totalCount);
                    }
                    
                    // 如果所有图片都已尝试加载，调用完成回调
                    if (loadedCount === totalCount) {
                        this.state.isLoaded = true;
                        
                        if (typeof this.config.onLoadComplete === 'function') {
                            this.config.onLoadComplete({
                                success: false,
                                loaded: this.state.loadedCount,
                                total: totalCount,
                                failed: totalCount - this.state.loadedCount
                            });
                        }
                        
                        resolve({
                            success: false,
                            loaded: this.state.loadedCount,
                            total: totalCount,
                            failed: totalCount - this.state.loadedCount
                        });
                    }
                };
                
                // 开始加载图片
                img.src = url;
            });
        });
    }
    
    /**
     * 渲染指定帧
     * @param {number} frameIndex 帧索引
     * @returns {ImageSequencePlayer} 当前实例，支持链式调用
     */
    renderFrame(frameIndex) {
        // 确保帧索引在有效范围内
        const index = Math.max(0, Math.min(Math.floor(frameIndex), this.state.images.length - 1));
        this.state.currentFrame = index;
        
        // 获取要渲染的图片
        const image = this.state.images[index];
        
        // 如果图片未加载，跳过渲染
        if (!image || !(image instanceof HTMLImageElement)) {
            return this;
        }
        
        // 清除Canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 根据适应模式计算渲染参数
        const renderParams = this._calculateRenderParams(image);
        
        // 渲染图片
        this.ctx.drawImage(
            image,
            renderParams.x,
            renderParams.y,
            renderParams.width,
            renderParams.height
        );
        
        // 调用帧渲染回调
        if (typeof this.config.onFrameRender === 'function') {
            this.config.onFrameRender(index, this.state.images.length);
        }
        
        return this;
    }
    
    /**
     * 播放序列
     * @param {number} startFrame 开始帧
     * @param {number} endFrame 结束帧
     * @param {Object} options 播放选项
     * @returns {ImageSequencePlayer} 当前实例，支持链式调用
     */
    playSequence(startFrame = 0, endFrame = null, options = {}) {
        console.log('🎬 playSequence 被调用');
        console.log('参数:', { startFrame, endFrame, options });
        console.log('当前图片数量:', this.state.images.length);
        console.log('图片加载状态:', this.state.isLoaded);
        console.log('已加载图片数:', this.state.loadedCount);

        // 如果没有图片，直接返回
        if (!this.state.images.length) {
            console.warn('⚠️ 没有图片可播放');
            return this;
        }

        // 设置结束帧（如果未指定）
        const lastFrame = this.state.images.length - 1;
        const finalEndFrame = endFrame !== null ? Math.min(endFrame, lastFrame) : lastFrame;

        // 确保开始帧和结束帧在有效范围内
        const finalStartFrame = Math.max(0, Math.min(startFrame, lastFrame));

        console.log('播放范围:', { finalStartFrame, finalEndFrame, lastFrame });

        // 更新状态
        this.state.startFrame = finalStartFrame;
        this.state.endFrame = finalEndFrame;
        this.state.currentFrame = finalStartFrame;
        this.state.duration = options.duration || 1000; // 默认1秒
        this.state.direction = options.reverse ? -1 : 1;
        this.state.loopCount = options.loop !== undefined ? options.loop : 0;
        this.state.currentLoop = 0;
        this.state.isPlaying = true;
        this.state.startTime = performance.now();

        console.log('播放状态设置完成:', {
            duration: this.state.duration,
            direction: this.state.direction,
            loopCount: this.state.loopCount,
            isPlaying: this.state.isPlaying
        });

        // 取消任何现有的动画
        if (this._rafId) {
            cancelAnimationFrame(this._rafId);
        }

        // 开始动画循环
        console.log('🎥 开始动画循环...');
        this._animate();

        return this;
    }
    
    /**
     * 暂停序列播放
     * @returns {ImageSequencePlayer} 当前实例，支持链式调用
     */
    pauseSequence() {
        this.state.isPlaying = false;
        
        // 取消动画
        if (this._rafId) {
            cancelAnimationFrame(this._rafId);
            this._rafId = null;
        }
        
        return this;
    }
    
    /**
     * 继续序列播放
     * @returns {ImageSequencePlayer} 当前实例，支持链式调用
     */
    resumeSequence() {
        if (!this.state.isPlaying) {
            this.state.isPlaying = true;
            this.state.startTime = performance.now() - 
                (this.state.currentFrame - this.state.startFrame) / 
                (Math.abs(this.state.endFrame - this.state.startFrame) + 1) * 
                this.state.duration;
                
            this._animate();
        }
        
        return this;
    }
    
    /**
     * 重置序列
     * @returns {ImageSequencePlayer} 当前实例，支持链式调用
     */
    resetSequence() {
        // 暂停任何播放
        this.pauseSequence();
        
        // 重置状态
        this.state.currentFrame = this.state.startFrame;
        this.state.currentLoop = 0;
        
        // 渲染第一帧
        this.renderFrame(this.state.currentFrame);
        
        return this;
    }
    
    /**
     * 设置当前帧（用于外部控制，如滚动动画）
     * @param {number} progress 进度值 (0-1)
     * @returns {ImageSequencePlayer} 当前实例，支持链式调用
     */
    setProgress(progress) {
        // 确保进度值在0-1范围内
        const clampedProgress = Math.max(0, Math.min(1, progress));
        
        // 计算帧索引
        const frameRange = this.state.images.length - 1;
        const frameIndex = Math.round(clampedProgress * frameRange);
        
        // 渲染帧
        this.renderFrame(frameIndex);
        
        return this;
    }
    
    /**
     * 获取当前帧索引
     * @returns {number} 当前帧索引
     */
    getCurrentFrame() {
        return this.state.currentFrame;
    }
    
    /**
     * 获取总帧数
     * @returns {number} 总帧数
     */
    getTotalFrames() {
        return this.state.images.length;
    }
    
    /**
     * 获取加载进度
     * @returns {number} 加载进度 (0-1)
     */
    getLoadProgress() {
        if (!this.state.images.length) return 0;
        return this.state.loadedCount / this.state.images.length;
    }
    
    /**
     * 销毁实例，释放资源
     */
    destroy() {
        // 停止任何动画
        this.pauseSequence();
        
        // 移除事件监听器
        if (this.config.autoSize) {
            window.removeEventListener('resize', this._boundResizeHandler);
        }
        
        // 清除Canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 释放图片引用
        this.state.images = [];
    }
    
    /**
     * 动画循环
     * @private
     */
    _animate() {
        if (!this.state.isPlaying) {
            console.log('⏸️ 动画已暂停');
            return;
        }

        const now = performance.now();
        const elapsed = now - this.state.startTime;
        const totalFrames = Math.abs(this.state.endFrame - this.state.startFrame) + 1;
        const duration = this.state.duration;

        // 计算当前进度
        let progress = (elapsed % duration) / duration;

        // 每隔一段时间输出调试信息
        // if (Math.floor(elapsed / 100) !== Math.floor((elapsed - 16) / 100)) {
        //     console.log('🎬 动画进度:', {
        //         elapsed: Math.round(elapsed),
        //         progress: progress.toFixed(3),
        //         totalFrames,
        //         currentFrame: this.state.currentFrame
        //     });
        // }
        
        // 如果进度超过1，检查是否需要循环
        if (elapsed >= duration) {
            // 增加循环计数
            this.state.currentLoop = Math.floor(elapsed / duration);
            
            // 检查是否达到循环次数限制
            if (this.state.loopCount > 0 && this.state.currentLoop >= this.state.loopCount) {
                // 设置为最后一帧
                this.renderFrame(this.state.direction > 0 ? this.state.endFrame : this.state.startFrame);
                this.state.isPlaying = false;
                return;
            }
        }
        
        // 计算当前帧索引
        let frameIndex;
        if (this.state.direction > 0) {
            // 正向播放
            frameIndex = this.state.startFrame + Math.floor(progress * totalFrames);
        } else {
            // 反向播放
            frameIndex = this.state.endFrame - Math.floor(progress * totalFrames);
        }
        
        // 确保帧索引在有效范围内
        frameIndex = Math.max(
            Math.min(this.state.startFrame, this.state.endFrame),
            Math.min(
                Math.max(this.state.startFrame, this.state.endFrame),
                frameIndex
            )
        );
        
        // 渲染当前帧
        this.renderFrame(frameIndex);
        
        // 继续动画循环
        this._rafId = requestAnimationFrame(() => this._animate());
    }
    
    /**
     * 计算图片渲染参数
     * @param {HTMLImageElement} image 图片元素
     * @returns {Object} 渲染参数
     * @private
     */
    _calculateRenderParams(image) {
        const canvasWidth = this.canvas.width;
        const canvasHeight = this.canvas.height;
        const imageWidth = image.naturalWidth;
        const imageHeight = image.naturalHeight;
        
        let width, height, x, y;
        
        // 根据适应模式计算尺寸
        switch (this.config.fitMode) {
            case 'contain':
                // 保持宽高比，完全显示图片
                const scale = Math.min(canvasWidth / imageWidth, canvasHeight / imageHeight);
                width = imageWidth * scale;
                height = imageHeight * scale;
                break;
                
            case 'cover':
                // 保持宽高比，填满Canvas
                const scaleCover = Math.max(canvasWidth / imageWidth, canvasHeight / imageHeight);
                width = imageWidth * scaleCover;
                height = imageHeight * scaleCover;
                break;
                
            case 'stretch':
                // 拉伸以填满Canvas
                width = canvasWidth;
                height = canvasHeight;
                break;
                
            default:
                // 默认为contain
                const scaleDefault = Math.min(canvasWidth / imageWidth, canvasHeight / imageHeight);
                width = imageWidth * scaleDefault;
                height = imageHeight * scaleDefault;
        }
        
        // 根据对齐方式计算位置
        switch (this.config.alignment) {
            case 'center':
                x = (canvasWidth - width) / 2;
                y = (canvasHeight - height) / 2;
                break;
                
            case 'top':
                x = (canvasWidth - width) / 2;
                y = 0;
                break;
                
            case 'bottom':
                x = (canvasWidth - width) / 2;
                y = canvasHeight - height;
                break;
                
            case 'left':
                x = 0;
                y = (canvasHeight - height) / 2;
                break;
                
            case 'right':
                x = canvasWidth - width;
                y = (canvasHeight - height) / 2;
                break;
                
            case 'top-left':
                x = 0;
                y = 0;
                break;
                
            case 'top-right':
                x = canvasWidth - width;
                y = 0;
                break;
                
            case 'bottom-left':
                x = 0;
                y = canvasHeight - height;
                break;
                
            case 'bottom-right':
                x = canvasWidth - width;
                y = canvasHeight - height;
                break;
                
            default:
                // 默认为center
                x = (canvasWidth - width) / 2;
                y = (canvasHeight - height) / 2;
        }
        
        return { x, y, width, height };
    }
    
    /**
     * 更新Canvas尺寸
     * @private
     */
    _updateCanvasSize() {
        const container = this.canvas.parentElement;
        if (!container) return;
        
        const rect = container.getBoundingClientRect();
        
        // 设置Canvas尺寸
        this.canvas.width = rect.width;
        this.canvas.height = rect.height;
        
        // 如果有当前帧，重新渲染
        if (this.state.images.length > 0) {
            this.renderFrame(this.state.currentFrame);
        }
    }
    
    /**
     * 处理窗口大小调整事件
     * @private
     */
    _handleResize() {
        // 使用requestAnimationFrame优化性能
        if (!this._rafId) {
            this._rafId = requestAnimationFrame(() => {
                this._updateCanvasSize();
                this._rafId = null;
            });
        }
    }
}

// 导出为全局变量和模块
if (typeof window !== 'undefined') {
    window.ImageSequencePlayer = ImageSequencePlayer;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = ImageSequencePlayer;
} 