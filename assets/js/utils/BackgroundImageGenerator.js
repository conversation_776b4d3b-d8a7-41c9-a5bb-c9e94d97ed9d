/**
 * BackgroundImageGenerator.js
 * 背景图片生成器，用于生成临时的科技感背景图片
 * 
 * {{CHENGQI:
 * Action: Added
 * Timestamp: 2025-01-10 20:15:00 +08:00
 * Reason: 创建临时背景图片生成器，为各section提供科技感背景
 * Principle_Applied: SOLID - 单一职责原则，专注于背景图片生成
 * Optimization: 使用Canvas API生成高质量的渐变和几何图形背景
 * Architectural_Note (AR): 作为临时解决方案，后续可替换为专业设计的图片
 * Documentation_Note (DW): 生成的图片将保存到 assets/images/backgrounds/ 目录
 * }}
 */

class BackgroundImageGenerator {
    constructor() {
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.canvas.width = 1920;
        this.canvas.height = 1080;
    }

    /**
     * 生成科技感背景图片
     */
    generateTechBackground() {
        const ctx = this.ctx;
        const canvas = this.canvas;

        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 1. 基础渐变背景
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
        gradient.addColorStop(0, '#0a0a0a');
        gradient.addColorStop(0.3, '#1a1a2e');
        gradient.addColorStop(0.6, '#16213e');
        gradient.addColorStop(1, '#0f3460');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 2. 网格线
        this._drawGrid(ctx, canvas);

        // 3. 发光圆圈
        this._drawGlowingCircles(ctx, canvas);

        // 4. 数据流线条
        this._drawDataStreams(ctx, canvas);

        // 5. 几何图形
        this._drawGeometricShapes(ctx, canvas);

        return canvas.toDataURL('image/webp', 0.8);
    }

    /**
     * 绘制网格
     * @private
     */
    _drawGrid(ctx, canvas) {
        ctx.strokeStyle = 'rgba(0, 122, 255, 0.1)';
        ctx.lineWidth = 1;

        const gridSize = 80;
        
        // 垂直线
        for (let x = 0; x < canvas.width; x += gridSize) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, canvas.height);
            ctx.stroke();
        }

        // 水平线
        for (let y = 0; y < canvas.height; y += gridSize) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(canvas.width, y);
            ctx.stroke();
        }
    }

    /**
     * 绘制发光圆圈
     * @private
     */
    _drawGlowingCircles(ctx, canvas) {
        const circles = [
            { x: canvas.width * 0.2, y: canvas.height * 0.3, radius: 150, color: 'rgba(0, 122, 255, 0.3)' },
            { x: canvas.width * 0.8, y: canvas.height * 0.7, radius: 200, color: 'rgba(48, 209, 88, 0.2)' },
            { x: canvas.width * 0.6, y: canvas.height * 0.2, radius: 100, color: 'rgba(88, 86, 214, 0.25)' }
        ];

        circles.forEach(circle => {
            // 创建径向渐变
            const gradient = ctx.createRadialGradient(
                circle.x, circle.y, 0,
                circle.x, circle.y, circle.radius
            );
            gradient.addColorStop(0, circle.color);
            gradient.addColorStop(1, 'transparent');

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(circle.x, circle.y, circle.radius, 0, Math.PI * 2);
            ctx.fill();

            // 外圈
            ctx.strokeStyle = circle.color.replace('0.3', '0.5').replace('0.2', '0.4').replace('0.25', '0.45');
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(circle.x, circle.y, circle.radius * 0.8, 0, Math.PI * 2);
            ctx.stroke();
        });
    }

    /**
     * 绘制数据流线条
     * @private
     */
    _drawDataStreams(ctx, canvas) {
        ctx.strokeStyle = 'rgba(0, 122, 255, 0.4)';
        ctx.lineWidth = 2;

        // 绘制流动的曲线
        const streams = [
            { startX: 0, startY: canvas.height * 0.3, endX: canvas.width, endY: canvas.height * 0.6 },
            { startX: 0, startY: canvas.height * 0.7, endX: canvas.width, endY: canvas.height * 0.4 },
            { startX: canvas.width * 0.2, startY: 0, endX: canvas.width * 0.8, endY: canvas.height }
        ];

        streams.forEach((stream, index) => {
            ctx.beginPath();
            ctx.moveTo(stream.startX, stream.startY);
            
            // 创建贝塞尔曲线
            const cp1x = canvas.width * (0.3 + index * 0.1);
            const cp1y = canvas.height * (0.2 + index * 0.2);
            const cp2x = canvas.width * (0.7 - index * 0.1);
            const cp2y = canvas.height * (0.8 - index * 0.2);
            
            ctx.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, stream.endX, stream.endY);
            ctx.stroke();

            // 添加发光效果
            ctx.shadowColor = 'rgba(0, 122, 255, 0.5)';
            ctx.shadowBlur = 10;
            ctx.stroke();
            ctx.shadowBlur = 0;
        });
    }

    /**
     * 绘制几何图形
     * @private
     */
    _drawGeometricShapes(ctx, canvas) {
        // 绘制一些科技感的几何图形
        const shapes = [
            { x: canvas.width * 0.1, y: canvas.height * 0.1, size: 60, rotation: 0 },
            { x: canvas.width * 0.9, y: canvas.height * 0.1, size: 80, rotation: Math.PI / 4 },
            { x: canvas.width * 0.1, y: canvas.height * 0.9, size: 70, rotation: Math.PI / 6 },
            { x: canvas.width * 0.9, y: canvas.height * 0.9, size: 50, rotation: Math.PI / 3 }
        ];

        shapes.forEach(shape => {
            ctx.save();
            ctx.translate(shape.x, shape.y);
            ctx.rotate(shape.rotation);

            // 绘制六边形
            ctx.strokeStyle = 'rgba(48, 209, 88, 0.3)';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            for (let i = 0; i < 6; i++) {
                const angle = (i * Math.PI) / 3;
                const x = Math.cos(angle) * shape.size;
                const y = Math.sin(angle) * shape.size;
                
                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            
            ctx.closePath();
            ctx.stroke();

            // 内部小圆
            ctx.fillStyle = 'rgba(48, 209, 88, 0.1)';
            ctx.beginPath();
            ctx.arc(0, 0, shape.size * 0.3, 0, Math.PI * 2);
            ctx.fill();

            ctx.restore();
        });
    }

    /**
     * 生成并下载背景图片
     */
    generateAndDownload(filename = 'technology-bg.webp') {
        const dataUrl = this.generateTechBackground();
        
        // 创建下载链接
        const link = document.createElement('a');
        link.download = filename;
        link.href = dataUrl;
        
        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        console.log(`✅ 背景图片已生成并下载: ${filename}`);
    }

    /**
     * 生成所有section的背景图片
     */
    generateAllBackgrounds() {
        // 这里可以扩展生成不同风格的背景图片
        this.generateAndDownload('technology-bg.webp');
        
        // 可以添加其他section的背景生成逻辑
        console.log('🎨 所有背景图片生成完成');
    }
}

// 导出为全局变量
if (typeof window !== 'undefined') {
    window.BackgroundImageGenerator = BackgroundImageGenerator;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = BackgroundImageGenerator;
}
