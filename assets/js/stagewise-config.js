/**
 * stagewise-config.js
 * Stagewise开发工具配置文件
 */

// 仅在开发环境中初始化
const isDevelopment = () => {
  return (
    window.location.hostname === 'localhost' || 
    window.location.hostname === '127.0.0.1' ||
    window.location.hostname.includes('.local')
  );
};

// Stagewise配置
const stagewiseConfig = {
  plugins: []
};

// 初始化Stagewise工具栏
const initStagewise = async () => {
  if (isDevelopment()) {
    try {
      // 动态导入stagewise工具栏
      const { initToolbar } = await import('https://esm.sh/@stagewise/toolbar');
      
      // 初始化工具栏
      initToolbar(stagewiseConfig);
      console.log('Stagewise开发工具栏已初始化');
    } catch (error) {
      console.error('Stagewise工具栏加载失败:', error);
    }
  }
};

// 导出配置和初始化函数
export { stagewiseConfig, initStagewise };

// 如果直接通过script标签引入，自动初始化
if (typeof window !== 'undefined' && typeof module === 'undefined') {
  window.addEventListener('DOMContentLoaded', () => {
    if (isDevelopment()) {
      // 创建script标签动态加载ESM模块
      const script = document.createElement('script');
      script.type = 'module';
      script.textContent = `
        import { initToolbar } from 'https://esm.sh/@stagewise/toolbar';
        
        const stagewiseConfig = {
          plugins: []
        };
        
        initToolbar(stagewiseConfig);
        console.log('Stagewise开发工具栏已初始化');
      `;
      document.head.appendChild(script);
    }
  });
} 