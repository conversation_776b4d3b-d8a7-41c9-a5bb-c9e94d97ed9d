/**
 * ContactForm.js
 * 联系表单组件，处理用户咨询和联系请求
 * 
 * {{CHENGQI:
 * Action: Added
 * Timestamp: 2025-01-11 17:30:00 +08:00
 * Reason: Per P3-LD-010 实现联系表单组件，提供用户联系渠道
 * Principle_Applied: SOLID - 单一职责原则，专注于表单处理；DRY - 复用表单验证和提交逻辑
 * Optimization: 实现客户端表单验证，提供良好的用户体验和错误提示
 * Architectural_Note (AR): 支持业务类型预选，与BusinessCards组件联动
 * Documentation_Note (DW): 相关架构文档见 /project_document/architecture/final_architecture_v1.0.md
 * }}
 */

class ContactForm {
    /**
     * 构造函数
     * @param {Object} options 配置选项
     */
    constructor(options = {}) {
        // 配置
        this.config = {
            formId: options.formId || 'contact-form',
            submitEndpoint: options.submitEndpoint || '/api/contact',
            enableValidation: options.enableValidation !== false,
            showSuccessMessage: options.showSuccessMessage !== false,
            debug: options.debug || false
        };

        // 状态
        this.state = {
            isInitialized: false,
            isSubmitting: false,
            formData: {},
            validationErrors: {}
        };

        // DOM元素
        this.elements = {
            form: null,
            fields: {},
            submitButton: null,
            messageContainer: null
        };

        // 验证规则
        this.validationRules = {
            name: {
                required: true,
                minLength: 2,
                maxLength: 50,
                pattern: /^[\u4e00-\u9fa5a-zA-Z\s]+$/
            },
            phone: {
                required: true,
                pattern: /^1[3-9]\d{9}$/
            },
            email: {
                required: false,
                pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            },
            company: {
                required: false,
                maxLength: 100
            },
            businessType: {
                required: true
            },
            message: {
                required: true,
                minLength: 10,
                maxLength: 500
            }
        };

        // 初始化
        this.init();
    }

    /**
     * 初始化组件
     */
    init() {
        try {
            // 1. 获取DOM元素
            this._getDOMElements();

            // 2. 渲染表单
            this._renderForm();

            // 3. 绑定事件
            this._bindEvents();

            // 4. 监听业务类型选择事件
            this._listenForBusinessSelection();

            this.state.isInitialized = true;
            console.log('📝 联系表单组件初始化完成');

        } catch (error) {
            console.error('❌ 联系表单组件初始化失败:', error);
        }
    }

    /**
     * 获取DOM元素
     * @private
     */
    _getDOMElements() {
        this.elements.form = document.getElementById(this.config.formId);
        
        if (!this.elements.form) {
            throw new Error(`联系表单 #${this.config.formId} 未找到`);
        }
    }

    /**
     * 渲染表单
     * @private
     */
    _renderForm() {
        const formHTML = `
            <div class="form-group">
                <label for="contact-name" class="form-label">姓名 *</label>
                <input type="text" id="contact-name" name="name" class="form-input" placeholder="请输入您的姓名" required>
                <div class="form-error" id="name-error"></div>
            </div>

            <div class="form-group">
                <label for="contact-phone" class="form-label">联系电话 *</label>
                <input type="tel" id="contact-phone" name="phone" class="form-input" placeholder="请输入您的手机号码" required>
                <div class="form-error" id="phone-error"></div>
            </div>

            <div class="form-group">
                <label for="contact-email" class="form-label">邮箱地址</label>
                <input type="email" id="contact-email" name="email" class="form-input" placeholder="请输入您的邮箱地址（可选）">
                <div class="form-error" id="email-error"></div>
            </div>

            <div class="form-group">
                <label for="contact-company" class="form-label">公司名称</label>
                <input type="text" id="contact-company" name="company" class="form-input" placeholder="请输入您的公司名称（可选）">
                <div class="form-error" id="company-error"></div>
            </div>

            <div class="form-group">
                <label for="contact-business-type" class="form-label">咨询业务 *</label>
                <select id="contact-business-type" name="businessType" class="form-select" required>
                    <option value="">请选择您感兴趣的业务</option>
                    <option value="risk-screening">公积金贷款贷前风险筛查</option>
                    <option value="commercial-to-public">公积金商转公担保业务</option>
                    <option value="second-hand-guarantee">公积金二手房担保业务</option>
                    <option value="asset-management">贷后资产委托管理</option>
                    <option value="other">其他业务咨询</option>
                </select>
                <div class="form-error" id="businessType-error"></div>
            </div>

            <div class="form-group">
                <label for="contact-message" class="form-label">详细需求 *</label>
                <textarea id="contact-message" name="message" class="form-textarea" placeholder="请详细描述您的需求或问题，我们将尽快为您提供专业解答..." required></textarea>
                <div class="form-error" id="message-error"></div>
            </div>

            <div class="form-group">
                <button type="submit" class="form-submit-btn" id="contact-submit">
                    <span class="submit-text">提交咨询</span>
                    <span class="submit-loading" style="display: none;">
                        <svg class="loading-spinner" width="16" height="16" viewBox="0 0 16 16">
                            <circle cx="8" cy="8" r="6" stroke="currentColor" stroke-width="2" fill="none" stroke-dasharray="37.7" stroke-dashoffset="37.7">
                                <animate attributeName="stroke-dashoffset" dur="1s" values="37.7;0;37.7" repeatCount="indefinite"/>
                            </circle>
                        </svg>
                        提交中...
                    </span>
                </button>
            </div>

            <div class="form-message" id="form-message"></div>

            <div class="form-privacy">
                <p>* 为必填项。我们承诺保护您的隐私信息，仅用于业务咨询回复。</p>
            </div>
        `;

        this.elements.form.innerHTML = formHTML;

        // 更新DOM元素引用
        this.elements.fields = {
            name: this.elements.form.querySelector('#contact-name'),
            phone: this.elements.form.querySelector('#contact-phone'),
            email: this.elements.form.querySelector('#contact-email'),
            company: this.elements.form.querySelector('#contact-company'),
            businessType: this.elements.form.querySelector('#contact-business-type'),
            message: this.elements.form.querySelector('#contact-message')
        };

        this.elements.submitButton = this.elements.form.querySelector('#contact-submit');
        this.elements.messageContainer = this.elements.form.querySelector('#form-message');
    }

    /**
     * 绑定事件
     * @private
     */
    _bindEvents() {
        // 表单提交事件
        this.elements.form.addEventListener('submit', this._handleSubmit.bind(this));

        // 实时验证事件
        if (this.config.enableValidation) {
            Object.keys(this.elements.fields).forEach(fieldName => {
                const field = this.elements.fields[fieldName];
                field.addEventListener('blur', () => this._validateField(fieldName));
                field.addEventListener('input', () => this._clearFieldError(fieldName));
            });
        }
    }

    /**
     * 监听业务类型选择事件
     * @private
     */
    _listenForBusinessSelection() {
        window.addEventListener('yinke:contact-request', (event) => {
            const { businessId } = event.detail;
            if (businessId && this.elements.fields.businessType) {
                this.elements.fields.businessType.value = businessId;
                this._validateField('businessType');
            }
        });
    }

    /**
     * 处理表单提交
     * @private
     */
    async _handleSubmit(event) {
        event.preventDefault();

        if (this.state.isSubmitting) {
            return;
        }

        // 验证表单
        if (!this._validateForm()) {
            return;
        }

        // 收集表单数据
        this._collectFormData();

        // 提交表单
        await this._submitForm();
    }

    /**
     * 验证整个表单
     * @private
     */
    _validateForm() {
        let isValid = true;
        this.state.validationErrors = {};

        Object.keys(this.validationRules).forEach(fieldName => {
            if (!this._validateField(fieldName)) {
                isValid = false;
            }
        });

        return isValid;
    }

    /**
     * 验证单个字段
     * @private
     */
    _validateField(fieldName) {
        const field = this.elements.fields[fieldName];
        const rules = this.validationRules[fieldName];
        const value = field.value.trim();

        let errorMessage = '';

        // 必填验证
        if (rules.required && !value) {
            errorMessage = '此项为必填项';
        }
        // 最小长度验证
        else if (rules.minLength && value.length < rules.minLength) {
            errorMessage = `最少需要${rules.minLength}个字符`;
        }
        // 最大长度验证
        else if (rules.maxLength && value.length > rules.maxLength) {
            errorMessage = `最多允许${rules.maxLength}个字符`;
        }
        // 格式验证
        else if (rules.pattern && value && !rules.pattern.test(value)) {
            errorMessage = this._getPatternErrorMessage(fieldName);
        }

        // 显示或清除错误
        if (errorMessage) {
            this._showFieldError(fieldName, errorMessage);
            this.state.validationErrors[fieldName] = errorMessage;
            return false;
        } else {
            this._clearFieldError(fieldName);
            delete this.state.validationErrors[fieldName];
            return true;
        }
    }

    /**
     * 获取格式错误信息
     * @private
     */
    _getPatternErrorMessage(fieldName) {
        const messages = {
            name: '姓名只能包含中文、英文和空格',
            phone: '请输入正确的手机号码',
            email: '请输入正确的邮箱地址'
        };
        return messages[fieldName] || '格式不正确';
    }

    /**
     * 显示字段错误
     * @private
     */
    _showFieldError(fieldName, message) {
        const errorElement = document.getElementById(`${fieldName}-error`);
        const field = this.elements.fields[fieldName];
        
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
        
        if (field) {
            field.classList.add('form-input--error');
        }
    }

    /**
     * 清除字段错误
     * @private
     */
    _clearFieldError(fieldName) {
        const errorElement = document.getElementById(`${fieldName}-error`);
        const field = this.elements.fields[fieldName];
        
        if (errorElement) {
            errorElement.style.display = 'none';
        }
        
        if (field) {
            field.classList.remove('form-input--error');
        }
    }

    /**
     * 收集表单数据
     * @private
     */
    _collectFormData() {
        this.state.formData = {};
        Object.keys(this.elements.fields).forEach(fieldName => {
            this.state.formData[fieldName] = this.elements.fields[fieldName].value.trim();
        });
        
        // 添加时间戳
        this.state.formData.timestamp = new Date().toISOString();
    }

    /**
     * 提交表单
     * @private
     */
    async _submitForm() {
        this.state.isSubmitting = true;
        this._setSubmitButtonLoading(true);

        try {
            // 模拟API调用（实际项目中替换为真实的API端点）
            await this._simulateApiCall();
            
            this._showSuccessMessage();
            this._resetForm();
            
        } catch (error) {
            console.error('表单提交失败:', error);
            this._showErrorMessage('提交失败，请稍后重试或直接联系我们');
        } finally {
            this.state.isSubmitting = false;
            this._setSubmitButtonLoading(false);
        }
    }

    /**
     * 模拟API调用
     * @private
     */
    async _simulateApiCall() {
        return new Promise((resolve) => {
            setTimeout(() => {
                console.log('表单数据:', this.state.formData);
                resolve();
            }, 2000);
        });
    }

    /**
     * 设置提交按钮加载状态
     * @private
     */
    _setSubmitButtonLoading(isLoading) {
        const submitText = this.elements.submitButton.querySelector('.submit-text');
        const submitLoading = this.elements.submitButton.querySelector('.submit-loading');
        
        if (isLoading) {
            submitText.style.display = 'none';
            submitLoading.style.display = 'flex';
            this.elements.submitButton.disabled = true;
        } else {
            submitText.style.display = 'block';
            submitLoading.style.display = 'none';
            this.elements.submitButton.disabled = false;
        }
    }

    /**
     * 显示成功消息
     * @private
     */
    _showSuccessMessage() {
        this.elements.messageContainer.innerHTML = `
            <div class="form-message--success">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                </svg>
                <span>提交成功！我们将在24小时内与您联系。</span>
            </div>
        `;
    }

    /**
     * 显示错误消息
     * @private
     */
    _showErrorMessage(message) {
        this.elements.messageContainer.innerHTML = `
            <div class="form-message--error">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"/>
                </svg>
                <span>${message}</span>
            </div>
        `;
    }

    /**
     * 重置表单
     * @private
     */
    _resetForm() {
        this.elements.form.reset();
        this.state.formData = {};
        this.state.validationErrors = {};
        
        // 清除所有错误状态
        Object.keys(this.elements.fields).forEach(fieldName => {
            this._clearFieldError(fieldName);
        });
    }

    /**
     * 销毁组件
     */
    destroy() {
        // 移除事件监听器
        this.elements.form.removeEventListener('submit', this._handleSubmit.bind(this));
        window.removeEventListener('yinke:contact-request', this._listenForBusinessSelection.bind(this));

        // 清空表单
        if (this.elements.form) {
            this.elements.form.innerHTML = '';
        }

        console.log('🗑️ 联系表单组件已销毁');
    }

    /**
     * 获取当前状态
     * @returns {Object} 当前状态
     */
    getState() {
        return { ...this.state };
    }
}

// 导出为全局变量和模块
if (typeof window !== 'undefined') {
    window.ContactForm = ContactForm;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = ContactForm;
}
