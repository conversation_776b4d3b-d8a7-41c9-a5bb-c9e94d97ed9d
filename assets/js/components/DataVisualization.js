/**
 * DataVisualization.js
 * 数据可视化组件，展示公司数据实力和科技能力
 * 
 * {{CHENGQI:
 * Action: Added
 * Timestamp: 2025-01-11 17:45:00 +08:00
 * Reason: Per P3-LD-009 实现数据可视化组件，展示公司数据实力
 * Principle_Applied: SOLID - 单一职责原则，专注于数据展示；DRY - 复用数据动画和渲染逻辑
 * Optimization: 使用Canvas和CSS动画结合，实现流畅的数据可视化效果
 * Architectural_Note (AR): 配合Intersection Observer实现滚动触发的数据动画
 * Documentation_Note (DW): 相关架构文档见 /project_document/architecture/final_architecture_v1.0.md
 * }}
 */

class DataVisualization {
    /**
     * 构造函数
     * @param {Object} options 配置选项
     */
    constructor(options = {}) {
        // 配置
        this.config = {
            containerId: options.containerId || 'technology-content',
            animationDuration: options.animationDuration || 2000,
            observerThreshold: options.observerThreshold || 0.3,
            enableCountAnimation: options.enableCountAnimation !== false,
            debug: options.debug || false
        };

        // 状态
        this.state = {
            isInitialized: false,
            isAnimating: false,
            animatedElements: new Set()
        };

        // DOM元素
        this.elements = {
            container: null,
            dataCards: [],
            techStack: null,
            aiModel: null
        };

        // Intersection Observer
        this.observer = null;

        // 数据
        this.data = {
            stats: [
                {
                    id: 'data-volume',
                    title: '数据处理量',
                    value: 1000000,
                    unit: '+',
                    suffix: '条/日',
                    description: '日均处理贷款申请数据',
                    icon: '📊',
                    color: '#007AFF'
                },
                {
                    id: 'ai-accuracy',
                    title: 'AI识别准确率',
                    value: 95.8,
                    unit: '%',
                    suffix: '',
                    description: '风险识别准确率',
                    icon: '🤖',
                    color: '#30D158'
                },
                {
                    id: 'response-time',
                    title: '响应时间',
                    value: 0.5,
                    unit: '',
                    suffix: '秒',
                    description: '平均风险评估响应时间',
                    icon: '⚡',
                    color: '#FF9500'
                },
                {
                    id: 'model-count',
                    title: '风控模型',
                    value: 12,
                    unit: '+',
                    suffix: '个',
                    description: '自主研发风控模型数量',
                    icon: '🧠',
                    color: '#AF52DE'
                }
            ],
            techStack: [
                { name: '大数据平台', level: 95, color: '#007AFF' },
                { name: '机器学习', level: 90, color: '#30D158' },
                { name: '深度学习', level: 85, color: '#FF9500' },
                { name: '自然语言处理', level: 80, color: '#AF52DE' },
                { name: '知识图谱', level: 88, color: '#FF3B30' },
                { name: '实时计算', level: 92, color: '#5856D6' }
            ]
        };

        // 初始化
        this.init();
    }

    /**
     * 初始化组件
     */
    init() {
        try {
            // 1. 获取DOM元素
            this._getDOMElements();

            // 2. 渲染数据可视化
            this._renderDataVisualization();

            // 3. 设置动画观察器
            this._setupIntersectionObserver();

            // 4. 绑定事件
            this._bindEvents();

            this.state.isInitialized = true;
            console.log('📈 数据可视化组件初始化完成');

        } catch (error) {
            console.error('❌ 数据可视化组件初始化失败:', error);
        }
    }

    /**
     * 获取DOM元素
     * @private
     */
    _getDOMElements() {
        this.elements.container = document.getElementById(this.config.containerId);
        
        if (!this.elements.container) {
            throw new Error(`数据可视化容器 #${this.config.containerId} 未找到`);
        }
    }

    /**
     * 渲染数据可视化
     * @private
     */
    _renderDataVisualization() {
        const visualizationHTML = `
            <div class="tech-visualization">
                <!-- 数据统计卡片 -->
                <div class="data-stats-grid">
                    ${this.data.stats.map(stat => this._renderStatCard(stat)).join('')}
                </div>

                <!-- 技术栈展示 -->
                <div class="tech-stack-section">
                    <h3 class="tech-stack-title">核心技术栈</h3>
                    <div class="tech-stack-grid">
                        ${this.data.techStack.map(tech => this._renderTechItem(tech)).join('')}
                    </div>
                </div>

                <!-- AI模型架构图 -->
                <div class="ai-model-section">
                    <h3 class="ai-model-title">智能风控模型架构</h3>
                    <div class="ai-model-diagram">
                        ${this._renderAIModelDiagram()}
                    </div>
                </div>
            </div>
        `;

        this.elements.container.innerHTML = visualizationHTML;

        // 更新DOM元素引用
        this.elements.dataCards = this.elements.container.querySelectorAll('.data-stat-card');
        this.elements.techStack = this.elements.container.querySelector('.tech-stack-grid');
        this.elements.aiModel = this.elements.container.querySelector('.ai-model-diagram');
    }

    /**
     * 渲染统计卡片
     * @private
     */
    _renderStatCard(stat) {
        return `
            <div class="data-stat-card" data-stat-id="${stat.id}" style="--accent-color: ${stat.color}">
                <div class="stat-icon">${stat.icon}</div>
                <div class="stat-content">
                    <div class="stat-value">
                        <span class="stat-number" data-target="${stat.value}">0</span>
                        <span class="stat-unit">${stat.unit}</span>
                        <span class="stat-suffix">${stat.suffix}</span>
                    </div>
                    <h4 class="stat-title">${stat.title}</h4>
                    <p class="stat-description">${stat.description}</p>
                </div>
            </div>
        `;
    }

    /**
     * 渲染技术项目
     * @private
     */
    _renderTechItem(tech) {
        return `
            <div class="tech-item" data-tech="${tech.name}">
                <div class="tech-info">
                    <span class="tech-name">${tech.name}</span>
                    <span class="tech-percentage">${tech.level}%</span>
                </div>
                <div class="tech-progress">
                    <div class="tech-progress-bar" 
                         style="--tech-color: ${tech.color}; --tech-level: ${tech.level}%">
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 渲染AI模型架构图
     * @private
     */
    _renderAIModelDiagram() {
        return `
            <div class="model-layer model-layer--input">
                <h4>数据输入层</h4>
                <div class="layer-items">
                    <div class="layer-item">个人信息</div>
                    <div class="layer-item">信用记录</div>
                    <div class="layer-item">收入证明</div>
                    <div class="layer-item">资产状况</div>
                </div>
            </div>
            
            <div class="model-arrow">
                <svg width="40" height="20" viewBox="0 0 40 20">
                    <path d="M0 10 L30 10 M25 5 L30 10 L25 15" stroke="currentColor" stroke-width="2" fill="none"/>
                </svg>
            </div>

            <div class="model-layer model-layer--processing">
                <h4>AI处理层</h4>
                <div class="layer-items">
                    <div class="layer-item">特征提取</div>
                    <div class="layer-item">风险建模</div>
                    <div class="layer-item">模式识别</div>
                    <div class="layer-item">智能决策</div>
                </div>
            </div>

            <div class="model-arrow">
                <svg width="40" height="20" viewBox="0 0 40 20">
                    <path d="M0 10 L30 10 M25 5 L30 10 L25 15" stroke="currentColor" stroke-width="2" fill="none"/>
                </svg>
            </div>

            <div class="model-layer model-layer--output">
                <h4>结果输出层</h4>
                <div class="layer-items">
                    <div class="layer-item">风险评分</div>
                    <div class="layer-item">决策建议</div>
                    <div class="layer-item">预警提示</div>
                    <div class="layer-item">报告生成</div>
                </div>
            </div>
        `;
    }

    /**
     * 设置Intersection Observer
     * @private
     */
    _setupIntersectionObserver() {
        if (!window.IntersectionObserver) {
            // 如果不支持Intersection Observer，直接显示所有元素
            this._animateAllElements();
            return;
        }

        this.observer = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this._animateElement(entry.target);
                    }
                });
            },
            {
                threshold: this.config.observerThreshold,
                rootMargin: '50px'
            }
        );

        // 观察所有可动画元素
        const animatableElements = this.elements.container.querySelectorAll(
            '.data-stat-card, .tech-item, .model-layer'
        );
        
        animatableElements.forEach(element => {
            this.observer.observe(element);
        });
    }

    /**
     * 动画显示元素
     * @private
     */
    _animateElement(element) {
        if (this.state.animatedElements.has(element)) {
            return; // 已经动画过了
        }

        this.state.animatedElements.add(element);

        if (element.classList.contains('data-stat-card')) {
            this._animateStatCard(element);
        } else if (element.classList.contains('tech-item')) {
            this._animateTechItem(element);
        } else if (element.classList.contains('model-layer')) {
            this._animateModelLayer(element);
        }
    }

    /**
     * 动画统计卡片
     * @private
     */
    _animateStatCard(cardElement) {
        cardElement.classList.add('data-stat-card--animated');

        if (this.config.enableCountAnimation) {
            const numberElement = cardElement.querySelector('.stat-number');
            const targetValue = parseFloat(numberElement.dataset.target);
            this._animateNumber(numberElement, 0, targetValue, this.config.animationDuration);
        }
    }

    /**
     * 动画技术项目
     * @private
     */
    _animateTechItem(techElement) {
        setTimeout(() => {
            techElement.classList.add('tech-item--animated');
        }, Math.random() * 500);
    }

    /**
     * 动画模型层
     * @private
     */
    _animateModelLayer(layerElement) {
        layerElement.classList.add('model-layer--animated');
        
        // 延迟动画子项目
        const items = layerElement.querySelectorAll('.layer-item');
        items.forEach((item, index) => {
            setTimeout(() => {
                item.classList.add('layer-item--animated');
            }, index * 100);
        });
    }

    /**
     * 数字计数动画
     * @private
     */
    _animateNumber(element, start, end, duration) {
        const startTime = performance.now();
        const isDecimal = end % 1 !== 0;

        const updateNumber = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = start + (end - start) * easeOutQuart;
            
            if (isDecimal) {
                element.textContent = current.toFixed(1);
            } else {
                element.textContent = Math.floor(current).toLocaleString();
            }

            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            } else {
                element.textContent = isDecimal ? end.toFixed(1) : end.toLocaleString();
            }
        };

        requestAnimationFrame(updateNumber);
    }

    /**
     * 显示所有元素（降级方案）
     * @private
     */
    _animateAllElements() {
        const animatableElements = this.elements.container.querySelectorAll(
            '.data-stat-card, .tech-item, .model-layer'
        );
        
        animatableElements.forEach((element, index) => {
            setTimeout(() => {
                this._animateElement(element);
            }, index * 200);
        });
    }

    /**
     * 绑定事件
     * @private
     */
    _bindEvents() {
        // 窗口大小调整事件
        window.addEventListener('resize', this._handleResize.bind(this));
    }

    /**
     * 处理窗口大小调整
     * @private
     */
    _handleResize() {
        // 重新计算布局
        if (this.state.isInitialized) {
            this._updateLayout();
        }
    }

    /**
     * 更新布局
     * @private
     */
    _updateLayout() {
        // 在移动端调整显示方式
        const isMobile = window.innerWidth < 768;
        
        if (isMobile) {
            this.elements.container.classList.add('tech-visualization--mobile');
        } else {
            this.elements.container.classList.remove('tech-visualization--mobile');
        }
    }

    /**
     * 销毁组件
     */
    destroy() {
        // 断开Intersection Observer
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }

        // 移除事件监听器
        window.removeEventListener('resize', this._handleResize.bind(this));

        // 清空容器
        if (this.elements.container) {
            this.elements.container.innerHTML = '';
        }

        console.log('🗑️ 数据可视化组件已销毁');
    }

    /**
     * 获取当前状态
     * @returns {Object} 当前状态
     */
    getState() {
        return { ...this.state };
    }

    /**
     * 处理窗口大小调整（公共方法）
     */
    handleResize() {
        this._handleResize();
    }
}

// 导出为全局变量和模块
if (typeof window !== 'undefined') {
    window.DataVisualization = DataVisualization;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataVisualization;
}
