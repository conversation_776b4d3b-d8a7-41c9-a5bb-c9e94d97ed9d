/**
 * SectionBackgroundManager.js
 * Section背景图片管理器，负责为各个section添加和管理背景图片
 * 
 * {{CHENGQI:
 * Action: Added
 * Timestamp: 2025-01-10 20:00:00 +08:00
 * Reason: 实现各section背景图片管理，提升网站视觉一致性
 * Principle_Applied: SOLID - 单一职责原则，专注于背景图片管理；DRY - 复用背景加载和显示逻辑
 * Optimization: 使用Intersection Observer实现懒加载，优化性能
 * Architectural_Note (AR): 与ResourceManager协作，实现高效的背景图片资源管理
 * Documentation_Note (DW): 相关架构文档见 /project_document/architecture/final_architecture_v1.0.md
 * }}
 */

class SectionBackgroundManager {
    /**
     * 构造函数
     * @param {Object} options 配置选项
     */
    constructor(options = {}) {
        // 配置
        this.config = {
            resourceManager: options.resourceManager,
            observerThreshold: options.observerThreshold || 0.1,
            fadeInDuration: options.fadeInDuration || 1000,
            debug: options.debug || false
        };

        // 状态
        this.state = {
            isInitialized: false,
            loadedBackgrounds: new Set(),
            observers: new Map()
        };

        // Section背景配置
        this.sectionConfigs = {
            'technology': {
                imageId: 'tech-background',
                imagePath: 'assets/images/backgrounds/technology-bg.svg',
                fallbackGradient: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)',
                overlayOpacity: 0.8
            },
            'about': {
                imageId: 'about-background',
                imagePath: 'assets/images/backgrounds/about-bg.svg',
                fallbackGradient: 'linear-gradient(135deg, #2c2c2e 0%, #1c1c1e 100%)',
                overlayOpacity: 0.7
            },
            'services': {
                imageId: 'services-background',
                imagePath: 'assets/images/backgrounds/services-bg.webp',
                fallbackGradient: 'linear-gradient(135deg, #000000 0%, #1a1a1a 100%)',
                overlayOpacity: 0.8
            },
            'partnership': {
                imageId: 'partnership-background',
                imagePath: 'assets/images/backgrounds/partnership-bg.webp',
                fallbackGradient: 'linear-gradient(135deg, #1c1c1e 0%, #2c2c2e 100%)',
                overlayOpacity: 0.7
            },
            'contact': {
                imageId: 'contact-background',
                imagePath: 'assets/images/backgrounds/contact-bg.webp',
                fallbackGradient: 'linear-gradient(135deg, #2c2c2e 0%, #1c1c1e 100%)',
                overlayOpacity: 0.6
            }
        };

        // 初始化
        this.init();
    }

    /**
     * 初始化组件
     */
    async init() {
        try {
            // 1. 预加载背景图片资源
            await this._preloadBackgroundImages();

            // 2. 设置各section的背景容器
            this._setupSectionBackgrounds();

            // 3. 设置Intersection Observer
            this._setupIntersectionObservers();

            this.state.isInitialized = true;
            console.log('🖼️ Section背景管理器初始化完成');

        } catch (error) {
            console.error('❌ Section背景管理器初始化失败:', error);
        }
    }

    /**
     * 预加载背景图片资源
     * @private
     */
    async _preloadBackgroundImages() {
        if (!this.config.resourceManager) {
            console.warn('ResourceManager未提供，将使用直接加载方式');
            return;
        }

        // 添加背景图片到资源管理器
        Object.entries(this.sectionConfigs).forEach(([sectionId, config]) => {
            this.config.resourceManager.add(
                config.imagePath,
                'image',
                config.imageId,
                8, // 中等优先级
                { group: 'section-backgrounds' }
            );
        });

        console.log('📦 背景图片资源已添加到资源管理器');
    }

    /**
     * 设置各section的背景容器
     * @private
     */
    _setupSectionBackgrounds() {
        Object.keys(this.sectionConfigs).forEach(sectionId => {
            const sectionElement = document.getElementById(sectionId);
            if (sectionElement) {
                this._createBackgroundContainer(sectionElement, sectionId);
            } else {
                console.warn(`Section元素未找到: ${sectionId}`);
            }
        });
    }

    /**
     * 创建背景容器
     * @private
     */
    _createBackgroundContainer(sectionElement, sectionId) {
        const config = this.sectionConfigs[sectionId];
        
        // 检查是否已存在背景容器
        let backgroundContainer = sectionElement.querySelector('.section-background');
        
        if (!backgroundContainer) {
            backgroundContainer = document.createElement('div');
            backgroundContainer.className = 'section-background';
            backgroundContainer.setAttribute('data-section', sectionId);
            
            // 插入到section的开头
            sectionElement.insertBefore(backgroundContainer, sectionElement.firstChild);
        }

        // 设置fallback背景
        backgroundContainer.style.background = config.fallbackGradient;
        
        console.log(`🎨 为 ${sectionId} section 创建背景容器`);
    }

    /**
     * 设置Intersection Observer
     * @private
     */
    _setupIntersectionObservers() {
        if (!window.IntersectionObserver) {
            // 降级方案：直接加载所有背景
            this._loadAllBackgrounds();
            return;
        }

        Object.keys(this.sectionConfigs).forEach(sectionId => {
            const sectionElement = document.getElementById(sectionId);
            if (sectionElement) {
                const observer = new IntersectionObserver(
                    (entries) => {
                        entries.forEach(entry => {
                            if (entry.isIntersecting && !this.state.loadedBackgrounds.has(sectionId)) {
                                this._loadSectionBackground(sectionId);
                            }
                        });
                    },
                    {
                        threshold: this.config.observerThreshold,
                        rootMargin: '100px'
                    }
                );

                observer.observe(sectionElement);
                this.state.observers.set(sectionId, observer);
            }
        });
    }

    /**
     * 加载指定section的背景
     * @private
     */
    async _loadSectionBackground(sectionId) {
        if (this.state.loadedBackgrounds.has(sectionId)) {
            return; // 已经加载过了
        }

        const config = this.sectionConfigs[sectionId];
        const sectionElement = document.getElementById(sectionId);
        const backgroundContainer = sectionElement?.querySelector('.section-background');

        if (!backgroundContainer) {
            console.error(`背景容器未找到: ${sectionId}`);
            return;
        }

        try {
            let backgroundImage = null;

            // 尝试从ResourceManager获取图片
            if (this.config.resourceManager) {
                backgroundImage = this.config.resourceManager.get(config.imageId, 'image');
            }

            // 如果ResourceManager中没有，则直接加载
            if (!backgroundImage) {
                backgroundImage = await this._loadImageDirect(config.imagePath);
            }

            if (backgroundImage) {
                this._displayBackgroundImage(backgroundContainer, backgroundImage, sectionId);
            } else {
                console.warn(`背景图片加载失败，使用fallback: ${sectionId}`);
            }

        } catch (error) {
            console.error(`背景图片加载失败: ${sectionId}`, error);
        }

        this.state.loadedBackgrounds.add(sectionId);
    }

    /**
     * 直接加载图片
     * @private
     */
    _loadImageDirect(imagePath) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = () => reject(new Error(`图片加载失败: ${imagePath}`));
            img.src = imagePath;
        });
    }

    /**
     * 显示背景图片
     * @private
     */
    _displayBackgroundImage(container, image, sectionId) {
        // 创建img元素
        const imgElement = document.createElement('img');
        imgElement.src = image.src;
        imgElement.alt = `${sectionId} background`;
        imgElement.style.opacity = '0';
        imgElement.style.transition = `opacity ${this.config.fadeInDuration}ms ease-out`;

        // 添加到容器
        container.appendChild(imgElement);

        // 触发淡入动画
        requestAnimationFrame(() => {
            imgElement.style.opacity = '1';
            container.classList.add('loaded');
        });

        console.log(`✅ ${sectionId} 背景图片加载完成`);
    }

    /**
     * 加载所有背景（降级方案）
     * @private
     */
    _loadAllBackgrounds() {
        Object.keys(this.sectionConfigs).forEach((sectionId, index) => {
            setTimeout(() => {
                this._loadSectionBackground(sectionId);
            }, index * 200);
        });
    }

    /**
     * 销毁组件
     */
    destroy() {
        // 断开所有Observer
        this.state.observers.forEach(observer => {
            observer.disconnect();
        });
        this.state.observers.clear();

        console.log('🗑️ Section背景管理器已销毁');
    }

    /**
     * 获取当前状态
     * @returns {Object} 当前状态
     */
    getState() {
        return { ...this.state };
    }
}

// 导出为全局变量和模块
if (typeof window !== 'undefined') {
    window.SectionBackgroundManager = SectionBackgroundManager;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = SectionBackgroundManager;
}
