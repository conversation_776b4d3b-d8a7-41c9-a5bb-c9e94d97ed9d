/**
 * HeroAnimation.js
 * 首屏Hero动画组件，实现Apple风格的Canvas动画效果
 * 
 * {{CHENGQI:
 * Action: Added
 * Timestamp: 2025-01-11 15:45:00 +08:00
 * Reason: Per P2-LD-006 实现首屏Hero动画组件，展示公司科技实力
 * Principle_Applied: SOLID - 单一职责原则，专注于首屏动画；开闭原则 - 支持不同性能级别的动画策略
 * Optimization: 根据设备性能智能选择动画复杂度，确保流畅体验
 * Architectural_Note (AR): 与ScrollAnimationEngine和ImageSequencePlayer协作，实现滚动同步的动画效果
 * Documentation_Note (DW): 相关架构文档见 /project_document/architecture/final_architecture_v1.0.md
 * }}
 * 
 * {{CHENGQI:
 * Action: Modified
 * Timestamp: 2025-01-10 16:40:00 +08:00
 * Reason: 修复ResourceManager资源获取类型不正确问题，统一资源接口使用
 * Principle_Applied: SOLID - 里氏替换原则，确保接口使用的一致性；DRY - 避免重复的类型检查逻辑
 * Optimization: 增加HTMLImageElement类型检查，提高代码健壮性
 * Architectural_Note (AR): 修复后确保与ResourceManager的接口契约一致，ResourceManager返回原始对象
 * Documentation_Note (DW): 问题分析和解决方案已记录在团队协作日志中
 * }}
 */

class HeroAnimation {
    /**
     * 构造函数
     * @param {Object} options 配置选项
     */
    constructor(options = {}) {
        // 配置
        this.config = {
            performanceLevel: options.performanceLevel || 'medium',
            resourceManager: options.resourceManager,
            scrollEngine: options.scrollEngine,
            canvasId: options.canvasId || 'hero-canvas',
            triggerElementId: options.triggerElementId || 'home',
            debug: options.debug || false,
            showTechElements: options.showTechElements !== undefined ? options.showTechElements : false
        };

        // 状态
        this.state = {
            isInitialized: false,
            isAnimating: false,
            currentProgress: 0,
            animationMode: 'static' // 'sequence', 'static', 'css'
        };

        // 组件实例
        this.components = {
            imageSequencePlayer: null,
            scrollAnimationEngine: null
        };

        // DOM元素
        this.elements = {
            canvas: null,
            heroSection: null,
            heroContent: null,
            heroTitle: null,
            heroSubtitle: null
        };

        // 初始化
        this.init();
    }

    /**
     * 初始化组件
     */
    async init() {
        try {
            // 1. 获取DOM元素
            this._getDOMElements();

            // 2. 根据性能级别选择动画模式
            this._selectAnimationMode();

            // 3. 初始化动画组件
            await this._initAnimationComponents();

            // 4. 设置滚动动画
            this._setupScrollAnimation();

            // 5. 初始化内容动画
            this._initContentAnimations();

            // 6. 绑定事件
            this._bindEvents();

            this.state.isInitialized = true;
            console.log('🎬 Hero动画组件初始化完成');

            // 自动播放入场动画
            setTimeout(() => {
                this.playEntranceAnimation();
            }, 500); // 延迟500ms确保页面完全加载

        } catch (error) {
            console.error('❌ Hero动画组件初始化失败:', error);
        }
    }

    /**
     * 获取DOM元素
     * @private
     */
    _getDOMElements() {
        this.elements.canvas = document.getElementById(this.config.canvasId);
        this.elements.heroSection = document.getElementById(this.config.triggerElementId);
        this.elements.heroContent = document.querySelector('.hero-content');
        this.elements.heroTitle = document.querySelector('.hero-title');
        this.elements.heroSubtitle = document.querySelector('.hero-subtitle');

        if (!this.elements.canvas) {
            throw new Error(`Canvas元素 #${this.config.canvasId} 未找到`);
        }

        if (!this.elements.heroSection) {
            throw new Error(`Hero区域 #${this.config.triggerElementId} 未找到`);
        }
    }

    /**
     * 根据性能级别选择动画模式
     * @private
     */
    _selectAnimationMode() {
        switch (this.config.performanceLevel) {
            case 'high':
                this.state.animationMode = 'sequence';
                break;
            case 'medium':
                // 中等性能设备：优先尝试序列动画，如果图片序列不可用则使用静态图片
                this.state.animationMode = 'sequence';
                break;
            case 'low':
                this.state.animationMode = 'static';
                break;
            default:
                this.state.animationMode = 'static';
        }

        console.log(`🎯 Hero动画模式: ${this.state.animationMode} (性能级别: ${this.config.performanceLevel})`);
    }

    /**
     * 初始化动画组件
     * @private
     */
    async _initAnimationComponents() {
        if (this.state.animationMode === 'sequence') {
            // 初始化图片序列播放器
            if (window.ImageSequencePlayer) {
                this.components.imageSequencePlayer = new ImageSequencePlayer(
                    this.elements.canvas,
                    null,
                    {
                        fitMode: 'cover',
                        alignment: 'center',
                        autoPreload: true, // 改为true，让setImages自动处理预加载
                        onLoadProgress: this._handleImageLoadProgress.bind(this),
                        onLoadComplete: this._handleImageLoadComplete.bind(this),
                        debug: this.config.debug
                    }
                );

                // 从资源管理器获取图片序列
                const sequenceLoaded = await this._loadImageSequence();

                // 如果序列图片加载失败，回退到静态图片
                if (!sequenceLoaded) {
                    console.log('🔄 图片序列不可用，回退到静态背景');
                    this.state.animationMode = 'static';
                    this._setupStaticBackground();
                }
            } else {
                console.log('🔄 ImageSequencePlayer不可用，回退到静态背景');
                this.state.animationMode = 'static';
                this._setupStaticBackground();
            }
        } else if (this.state.animationMode === 'static') {
            // 静态模式：显示背景图片
            this._setupStaticBackground();
        }
    }

    /**
     * 加载图片序列
     * @private
     * @returns {Promise<boolean>} 是否成功加载图片序列
     */
    async _loadImageSequence() {
        if (!this.config.resourceManager) {
            console.warn('资源管理器未提供，无法加载图片序列');
            return false;
        }

        // 根据性能级别获取不同的图片序列
        const frameCount = this.config.performanceLevel === 'high' ? 60 : 2;
        const imageSequence = [];

        console.log(`🔍 尝试加载 ${frameCount} 帧图片序列...`);

        for (let i = 1; i <= frameCount; i++) {
            const frameId = `frame_${String(i).padStart(3, '0')}`;
            console.log(`🔍 查找图片 ID: ${frameId}`);
            const image = this.config.resourceManager.get(frameId, 'image');
            if (image && image instanceof HTMLImageElement) {
                // ResourceManager返回的是原始Image对象，直接使用
                console.log(`✅ 找到图片: ${frameId}, src: ${image.src}`);
                imageSequence.push(image.src);
            } else {
                console.warn(`❌ 未找到图片: ${frameId}`);
            }
        }
        if (imageSequence.length > 0) {
            // 设置图片序列，由于autoPreload=true，setImages会自动调用preloadImages
            // 这样避免了手动调用preloadImages的重复逻辑
            this.components.imageSequencePlayer.setImages(imageSequence);

            // 等待预加载完成（通过Promise包装回调）
            // 因为setImages内部的preloadImages是异步的，我们需要等待它完成
            await new Promise((resolve) => {
                // 如果已经加载完成，直接resolve
                if (this.components.imageSequencePlayer.state.isLoaded) {
                    resolve();
                    return;
                }

                // 否则等待加载完成回调
                const originalCallback = this.components.imageSequencePlayer.config.onLoadComplete;
                this.components.imageSequencePlayer.config.onLoadComplete = (result) => {
                    // 调用原始回调
                    if (originalCallback) {
                        originalCallback(result);
                    }
                    // 解析Promise
                    resolve();
                };
            });

            console.log(`✅ 成功加载 ${imageSequence.length} 帧图片序列`);
            return true;
        } else {
            console.warn('❌ 从资源管理器未找到图片序列帧，尝试直接加载图片...');

            // 备用方案：直接使用图片URL
            const fallbackImageUrls = [];
            for (let i = 1; i <= frameCount; i++) {
                const filename = `frame_${String(i).padStart(3, '0')}.webp`;
                fallbackImageUrls.push(`assets/images/hero-sequence/${filename}`);
            }

            if (fallbackImageUrls.length > 0) {
                console.log(`🔄 使用备用方案加载 ${fallbackImageUrls.length} 张图片...`);
                this.components.imageSequencePlayer.setImages(fallbackImageUrls);

                // 等待预加载完成
                await new Promise((resolve) => {
                    const originalCallback = this.components.imageSequencePlayer.config.onLoadComplete;
                    this.components.imageSequencePlayer.config.onLoadComplete = (result) => {
                        if (originalCallback) {
                            originalCallback(result);
                        }
                        resolve();
                    };
                });

                console.log(`✅ 备用方案成功加载 ${fallbackImageUrls.length} 帧图片序列`);
                return true;
            }

            return false;
        }
    }

    /**
     * 设置静态背景
     * @private
     */
    _setupStaticBackground() {
        // 尝试加载静态背景图片
        this._loadStaticBackgroundImage();
        // this._drawGradientBackground();
    }

    /**
     * 加载静态背景图片
     * @private
     */
    _loadStaticBackgroundImage() {
        console.log('🖼️ 开始加载静态背景图片...');

        // 首先尝试从资源管理器获取图片
        if (this.config.resourceManager) {
            console.log('📦 尝试从资源管理器获取图片...');
            const staticImage = this.config.resourceManager.get('hero-static', 'image');
            if (staticImage && staticImage instanceof HTMLImageElement) {
                console.log('✅ 从资源管理器获取到图片，开始绘制');
                // ResourceManager返回的是原始Image对象，直接使用
                this._drawImageBackground(staticImage);
                return;
            } else {
                console.log('❌ 资源管理器中未找到图片，尝试直接加载');
            }
        } else {
            console.log('❌ 资源管理器不可用，尝试直接加载');
        }

        // 如果资源管理器中没有，则直接加载
        const img = new Image();
        img.onload = () => {
            console.log('✅ 图片直接加载成功，开始绘制');
            this._drawImageBackground(img);
        };
        img.onerror = () => {
            console.warn('❌ 静态背景图片加载失败，使用渐变背景');
            this._drawGradientBackground();
        };
        img.src = 'assets/images/hero-static.webp';
        console.log('🔄 开始直接加载图片:', img.src);
    }

    /**
     * 绘制图片背景
     * @private
     */
    _drawImageBackground(img) {
        console.log('🎨 开始绘制图片背景...');
        const ctx = this.elements.canvas.getContext('2d');
        const canvas = this.elements.canvas;

        // 调整Canvas尺寸
        this._resizeCanvas();
        console.log(`📐 Canvas尺寸: ${canvas.width}x${canvas.height}`);

        // 计算图片缩放比例以覆盖整个canvas
        const canvasRatio = canvas.width / canvas.height;
        const imgRatio = img.width / img.height;
        console.log(`🖼️ 图片尺寸: ${img.width}x${img.height}, 比例: ${imgRatio.toFixed(2)}`);
        console.log(`📺 Canvas比例: ${canvasRatio.toFixed(2)}`);

        let drawWidth, drawHeight, offsetX, offsetY;

        if (canvasRatio > imgRatio) {
            // Canvas更宽，以宽度为准
            drawWidth = canvas.width;
            drawHeight = canvas.width / imgRatio;
            offsetX = 0;
            offsetY = (canvas.height - drawHeight) / 2;
        } else {
            // Canvas更高，以高度为准
            drawHeight = canvas.height;
            drawWidth = canvas.height * imgRatio;
            offsetX = (canvas.width - drawWidth) / 2;
            offsetY = 0;
        }

        // 绘制图片
        ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);

        // 添加轻微的半透明遮罩以确保文字可读性（降低透明度）
        const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
        gradient.addColorStop(0, 'rgba(0, 0, 0, 0.15)'); // 减少遮罩透明度
        gradient.addColorStop(0.5, 'rgba(0, 0, 0, 0.1)'); // 减少遮罩透明度
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0.2)'); // 减少遮罩透明度

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 根据配置决定是否添加科技感元素
        if (this.config.showTechElements) {
            this._drawTechElements(ctx);
        }
    }

    /**
     * 绘制渐变背景（备用方案）
     * @private
     */
    _drawGradientBackground() {
        const ctx = this.elements.canvas.getContext('2d');
        const canvas = this.elements.canvas;

        // 调整Canvas尺寸
        this._resizeCanvas();

        // 创建渐变
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
        gradient.addColorStop(0, '#1d1d1f');
        gradient.addColorStop(0.5, '#007aff');
        gradient.addColorStop(1, '#5856d6');

        // 填充渐变
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 根据配置决定是否添加科技感元素
        if (this.config.showTechElements) {
            this._drawTechElements(ctx);
        }
    }

    /**
     * 绘制科技元素
     * @private
     */
    _drawTechElements(ctx) {
        const canvas = this.elements.canvas;
        
        // 绘制网格
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.lineWidth = 1;
        
        const gridSize = 50;
        for (let x = 0; x < canvas.width; x += gridSize) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, canvas.height);
            ctx.stroke();
        }
        
        for (let y = 0; y < canvas.height; y += gridSize) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(canvas.width, y);
            ctx.stroke();
        }

        // 绘制发光圆圈
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        
        ctx.beginPath();
        ctx.arc(centerX, centerY, 100, 0, Math.PI * 2);
        ctx.strokeStyle = 'rgba(0, 122, 255, 0.5)';
        ctx.lineWidth = 2;
        ctx.stroke();
        
        ctx.beginPath();
        ctx.arc(centerX, centerY, 150, 0, Math.PI * 2);
        ctx.strokeStyle = 'rgba(0, 122, 255, 0.3)';
        ctx.lineWidth = 1;
        ctx.stroke();
    }

    /**
     * 调整Canvas尺寸
     * @private
     */
    _resizeCanvas() {
        const container = this.elements.canvas.parentElement;
        if (container) {
            const rect = container.getBoundingClientRect();
            this.elements.canvas.width = rect.width;
            this.elements.canvas.height = rect.height;
        }
    }

    /**
     * 设置滚动动画
     * @private
     */
    _setupScrollAnimation() {
        if (this.config.scrollEngine && window.ScrollAnimationEngine) {
            this.components.scrollAnimationEngine = new ScrollAnimationEngine({
                triggerElement: this.elements.heroSection,
                onProgressChange: this._handleScrollProgress.bind(this),
                debug: this.config.debug
            });
        }
    }

    /**
     * 初始化内容动画
     * @private
     */
    _initContentAnimations() {
        // 确保文字始终可见，避免动画失败导致不显示
        if (this.elements.heroTitle) {
            // 设置初始状态，但确保有备用显示
            this.elements.heroTitle.style.opacity = '1'; // 改为默认可见
            this.elements.heroTitle.style.transform = 'translateY(0)'; // 改为默认位置
            this.elements.heroTitle.style.zIndex = '15'; // 确保在最上层
        }

        if (this.elements.heroSubtitle) {
            // 设置初始状态，但确保有备用显示
            this.elements.heroSubtitle.style.opacity = '1'; // 改为默认可见
            this.elements.heroSubtitle.style.transform = 'translateY(0)'; // 改为默认位置
            this.elements.heroSubtitle.style.zIndex = '15'; // 确保在最上层
        }

        // 延迟显示内容动画（如果需要动画效果）
        setTimeout(() => {
            this._animateContentIn();
        }, 500); // 减少延迟时间
    }

    /**
     * 内容入场动画
     * @private
     */
    _animateContentIn() {
        if (this.elements.heroTitle) {
            this.elements.heroTitle.style.transition = 'all 1s ease-out';
            this.elements.heroTitle.style.opacity = '1';
            this.elements.heroTitle.style.transform = 'translateY(0)';
        }

        setTimeout(() => {
            if (this.elements.heroSubtitle) {
                this.elements.heroSubtitle.style.transition = 'all 1s ease-out';
                this.elements.heroSubtitle.style.opacity = '1';
                this.elements.heroSubtitle.style.transform = 'translateY(0)';
            }
        }, 300);
    }

    /**
     * 绑定事件
     * @private
     */
    _bindEvents() {
        // 窗口大小调整事件
        window.addEventListener('resize', this._handleResize.bind(this));
    }

    /**
     * 处理滚动进度变化
     * @private
     */
    _handleScrollProgress(progress) {
        this.state.currentProgress = progress;

        if (this.state.animationMode === 'sequence' && this.components.imageSequencePlayer) {
            // 根据滚动进度更新图片序列
            this.components.imageSequencePlayer.setProgress(progress);
        }

        // 更新内容的视差效果
        this._updateParallaxEffect(progress);
    }

    /**
     * 更新视差效果
     * @private
     */
    _updateParallaxEffect(progress) {
        if (this.elements.heroContent) {
            // 内容向上移动的视差效果
            const translateY = progress * 100;
            const opacity = 1 - progress * 0.8;

            this.elements.heroContent.style.transform = `translateY(${translateY}px)`;
            this.elements.heroContent.style.opacity = Math.max(0, opacity);
        }
    }

    /**
     * 处理图片加载进度
     * @private
     */
    _handleImageLoadProgress(progress, loaded, total) {
        if (this.config.debug) {
            console.log(`Hero图片加载进度: ${Math.round(progress * 100)}% (${loaded}/${total})`);
        }
    }

    /**
     * 处理图片加载完成
     * @private
     */
    _handleImageLoadComplete(result) {
        console.log('🖼️ Hero图片序列加载完成:', result);

        if (result.success && this.components.imageSequencePlayer) {
            // 渲染第一帧
            this.components.imageSequencePlayer.renderFrame(0);
        }
    }

    /**
     * 处理窗口大小调整
     * @private
     */
    _handleResize() {
        if (this.state.animationMode === 'static') {
            // 重新绘制静态背景
            this._setupStaticBackground();
        } else if (this.components.imageSequencePlayer) {
            // 重新渲染当前帧
            this.components.imageSequencePlayer.renderFrame(
                this.components.imageSequencePlayer.getCurrentFrame()
            );
        }
    }

    /**
     * 播放入场动画
     */
    playEntranceAnimation() {
        console.log('🎬 开始播放入场动画...');
        console.log('动画模式:', this.state.animationMode);
        console.log('图片序列播放器:', this.components.imageSequencePlayer);

        if (this.state.animationMode === 'sequence' && this.components.imageSequencePlayer) {
            // 检查图片是否已加载
            const totalFrames = this.components.imageSequencePlayer.getTotalFrames();
            console.log('总帧数:', totalFrames);

            if (totalFrames > 0) {
                // 播放图片序列动画
                console.log('🎥 播放图片序列动画...');
                this.components.imageSequencePlayer.playSequence(0, totalFrames - 1, {
                    duration: 2000,
                    loop: 0
                });
            } else {
                console.warn('⚠️ 图片序列未加载，无法播放动画');
            }
        } else {
            console.log('📷 非序列模式或播放器不可用');
        }

        // 播放内容动画
        this._animateContentIn();
    }

    /**
     * 暂停动画
     */
    pause() {
        if (this.components.imageSequencePlayer) {
            this.components.imageSequencePlayer.pauseSequence();
        }
        this.state.isAnimating = false;
    }

    /**
     * 恢复动画
     */
    resume() {
        if (this.components.imageSequencePlayer) {
            this.components.imageSequencePlayer.resumeSequence();
        }
        this.state.isAnimating = true;
    }

    /**
     * 销毁组件
     */
    destroy() {
        // 销毁图片序列播放器
        if (this.components.imageSequencePlayer) {
            this.components.imageSequencePlayer.destroy();
        }

        // 销毁滚动动画引擎
        if (this.components.scrollAnimationEngine) {
            this.components.scrollAnimationEngine.destroy();
        }

        // 移除事件监听器
        window.removeEventListener('resize', this._handleResize.bind(this));

        console.log('🗑️ Hero动画组件已销毁');
    }

    /**
     * 获取当前状态
     * @returns {Object} 当前状态
     */
    getState() {
        return { ...this.state };
    }

    /**
     * 处理窗口大小调整（公共方法）
     */
    handleResize() {
        this._handleResize();
    }
}

// 导出为全局变量和模块
if (typeof window !== 'undefined') {
    window.HeroAnimation = HeroAnimation;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = HeroAnimation;
}
