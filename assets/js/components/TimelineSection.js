/**
 * TimelineSection.js
 * 公司时间轴组件，展示湖北银科的发展历程
 * 
 * {{CHENGQI:
 * Action: Added
 * Timestamp: 2025-01-11 16:30:00 +08:00
 * Reason: Per P2-LD-007 实现公司时间轴组件，展示公司发展历程和专业积淀
 * Principle_Applied: SOLID - 单一职责原则，专注于时间轴展示；DRY - 复用时间节点渲染逻辑
 * Optimization: 使用Intersection Observer API优化滚动触发动画，提升性能
 * Architectural_Note (AR): 配合ScrollAnimationEngine实现滚动触发的渐进式动画效果
 * Documentation_Note (DW): 相关架构文档见 /project_document/architecture/final_architecture_v1.0.md
 * }}
 */

class TimelineSection {
    /**
     * 构造函数
     * @param {Object} options 配置选项
     */
    constructor(options = {}) {
        // 配置
        this.config = {
            containerId: options.containerId || 'about-timeline',
            animationDelay: options.animationDelay || 200,
            observerThreshold: options.observerThreshold || 0.3,
            debug: options.debug || false
        };

        // 状态
        this.state = {
            isInitialized: false,
            timelineData: [],
            animatedItems: new Set()
        };

        // DOM元素
        this.elements = {
            container: null,
            timelineItems: []
        };

        // Intersection Observer
        this.observer = null;

        // 初始化
        this.init();
    }

    /**
     * 初始化组件
     */
    init() {
        try {
            // 1. 获取DOM元素
            this._getDOMElements();

            // 2. 准备时间轴数据
            this._prepareTimelineData();

            // 3. 渲染时间轴
            this._renderTimeline();

            // 4. 设置动画观察器
            this._setupIntersectionObserver();

            // 5. 绑定事件
            this._bindEvents();

            this.state.isInitialized = true;
            console.log('📅 时间轴组件初始化完成');

        } catch (error) {
            console.error('❌ 时间轴组件初始化失败:', error);
        }
    }

    /**
     * 获取DOM元素
     * @private
     */
    _getDOMElements() {
        this.elements.container = document.getElementById(this.config.containerId);
        
        if (!this.elements.container) {
            throw new Error(`时间轴容器 #${this.config.containerId} 未找到`);
        }
    }

    /**
     * 准备时间轴数据
     * @private
     */
    _prepareTimelineData() {
        this.state.timelineData = [
            {
                year: '2009',
                month: '7月',
                title: '公司成立',
                description: '湖北银科融资担保有限公司正式成立，注册资本1亿元',
                icon: '🏢',
                type: 'milestone'
            },
            {
                year: '2010-2015',
                title: '业务拓展期',
                description: '深耕公积金贷款担保服务领域，积累丰富经验',
                icon: '📈',
                type: 'period'
            },
            {
                year: '2016-2020',
                title: '技术创新期',
                description: '开始探索大数据与AI技术在风险防控中的应用',
                icon: '🔬',
                type: 'period'
            },
            {
                year: '2021',
                title: '风控体系完善',
                description: '建立完善的贷前全向筛查、贷中适时监控及贷后打包处置体系',
                icon: '🛡️',
                type: 'achievement'
            },
            {
                year: '2022',
                month: '4月',
                title: '宜昌合作启动',
                description: '与宜昌住房公积金中心签订合作协议，开办"商转公"贷款担保业务',
                icon: '🤝',
                type: 'milestone'
            },
            {
                year: '2022',
                month: '11月',
                title: '业务范围扩展',
                description: '开展灵活就业人员购房担保、现房贷款担保、二手房公积金贷款担保业务',
                icon: '🏠',
                type: 'milestone'
            },
            {
                year: '2022',
                month: '12月',
                title: '三峡中心合作',
                description: '与宜昌住房公积金（三峡中心）签订合作协议',
                icon: '⛰️',
                type: 'milestone'
            },
            {
                year: '2023-至今',
                title: '数字化转型',
                description: '全面推进数字化转型，成为行业内唯一自主完成大数据模型开发的融资担保公司',
                icon: '🚀',
                type: 'current'
            }
        ];
    }

    /**
     * 渲染时间轴
     * @private
     */
    _renderTimeline() {
        const timelineHTML = `
            <div class="timeline-container">
                <div class="timeline-line"></div>
                ${this.state.timelineData.map((item, index) => this._renderTimelineItem(item, index)).join('')}
            </div>
        `;

        this.elements.container.innerHTML = timelineHTML;
        
        // 更新时间轴项目元素引用
        this.elements.timelineItems = this.elements.container.querySelectorAll('.timeline-item');
    }

    /**
     * 渲染单个时间轴项目
     * @private
     */
    _renderTimelineItem(item, index) {
        const isLeft = index % 2 === 0;
        const typeClass = `timeline-item--${item.type}`;
        const positionClass = isLeft ? 'timeline-item--left' : 'timeline-item--right';

        return `
            <div class="timeline-item ${typeClass} ${positionClass}" data-index="${index}">
                <div class="timeline-marker">
                    <div class="timeline-icon">${item.icon}</div>
                </div>
                <div class="timeline-content">
                    <div class="timeline-date">
                        <span class="timeline-year">${item.year}</span>
                        ${item.month ? `<span class="timeline-month">${item.month}</span>` : ''}
                    </div>
                    <h3 class="timeline-title">${item.title}</h3>
                    <p class="timeline-description">${item.description}</p>
                </div>
            </div>
        `;
    }

    /**
     * 设置Intersection Observer
     * @private
     */
    _setupIntersectionObserver() {
        if (!window.IntersectionObserver) {
            // 如果不支持Intersection Observer，直接显示所有项目
            this._showAllItems();
            return;
        }

        this.observer = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this._animateTimelineItem(entry.target);
                    }
                });
            },
            {
                threshold: this.config.observerThreshold,
                rootMargin: '50px'
            }
        );

        // 观察所有时间轴项目
        this.elements.timelineItems.forEach(item => {
            this.observer.observe(item);
        });
    }

    /**
     * 动画显示时间轴项目
     * @private
     */
    _animateTimelineItem(element) {
        const index = parseInt(element.dataset.index);
        
        if (this.state.animatedItems.has(index)) {
            return; // 已经动画过了
        }

        this.state.animatedItems.add(index);

        // 添加动画延迟
        setTimeout(() => {
            element.classList.add('timeline-item--animated');
            
            // 动画时间线连接线
            this._animateTimelineLine(index);
            
        }, index * this.config.animationDelay);
    }

    /**
     * 动画时间线连接线
     * @private
     */
    _animateTimelineLine(index) {
        const timelineLine = this.elements.container.querySelector('.timeline-line');
        if (timelineLine) {
            const progress = (index + 1) / this.state.timelineData.length;
            timelineLine.style.setProperty('--timeline-progress', `${progress * 100}%`);
        }
    }

    /**
     * 显示所有项目（降级方案）
     * @private
     */
    _showAllItems() {
        this.elements.timelineItems.forEach((item, index) => {
            setTimeout(() => {
                item.classList.add('timeline-item--animated');
            }, index * this.config.animationDelay);
        });

        // 完整显示时间线
        const timelineLine = this.elements.container.querySelector('.timeline-line');
        if (timelineLine) {
            timelineLine.style.setProperty('--timeline-progress', '100%');
        }
    }

    /**
     * 绑定事件
     * @private
     */
    _bindEvents() {
        // 窗口大小调整事件
        window.addEventListener('resize', this._handleResize.bind(this));
    }

    /**
     * 处理窗口大小调整
     * @private
     */
    _handleResize() {
        // 重新计算时间轴布局
        if (this.state.isInitialized) {
            this._updateTimelineLayout();
        }
    }

    /**
     * 更新时间轴布局
     * @private
     */
    _updateTimelineLayout() {
        // 在移动端，所有项目都显示在右侧
        const isMobile = window.innerWidth < 768;
        
        this.elements.timelineItems.forEach((item, index) => {
            if (isMobile) {
                item.classList.remove('timeline-item--left');
                item.classList.add('timeline-item--right');
            } else {
                const isLeft = index % 2 === 0;
                item.classList.toggle('timeline-item--left', isLeft);
                item.classList.toggle('timeline-item--right', !isLeft);
            }
        });
    }

    /**
     * 销毁组件
     */
    destroy() {
        // 断开Intersection Observer
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }

        // 移除事件监听器
        window.removeEventListener('resize', this._handleResize.bind(this));

        // 清空容器
        if (this.elements.container) {
            this.elements.container.innerHTML = '';
        }

        console.log('🗑️ 时间轴组件已销毁');
    }

    /**
     * 获取当前状态
     * @returns {Object} 当前状态
     */
    getState() {
        return { ...this.state };
    }

    /**
     * 处理窗口大小调整（公共方法）
     */
    handleResize() {
        this._handleResize();
    }
}

// 导出为全局变量和模块
if (typeof window !== 'undefined') {
    window.TimelineSection = TimelineSection;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = TimelineSection;
}
