/**
 * BusinessCards.js
 * 业务服务卡片组件，展示湖北银科的四大核心业务
 * 
 * {{CHENGQI:
 * Action: Added
 * Timestamp: 2025-01-11 17:00:00 +08:00
 * Reason: Per P2-LD-008 实现业务服务卡片组件，展示四大核心业务能力
 * Principle_Applied: SOLID - 单一职责原则，专注于业务卡片展示；DRY - 复用卡片渲染和动画逻辑
 * Optimization: 使用Intersection Observer优化滚动触发动画，支持卡片翻转和悬停效果
 * Architectural_Note (AR): 配合响应式设计，在不同设备上提供最佳的卡片布局体验
 * Documentation_Note (DW): 相关架构文档见 /project_document/architecture/final_architecture_v1.0.md
 * }}
 */

class BusinessCards {
    /**
     * 构造函数
     * @param {Object} options 配置选项
     */
    constructor(options = {}) {
        // 配置
        this.config = {
            containerId: options.containerId || 'services-grid',
            animationDelay: options.animationDelay || 150,
            observerThreshold: options.observerThreshold || 0.2,
            enableFlipEffect: options.enableFlipEffect !== false,
            debug: options.debug || false
        };

        // 状态
        this.state = {
            isInitialized: false,
            businessData: [],
            animatedCards: new Set(),
            flippedCards: new Set()
        };

        // DOM元素
        this.elements = {
            container: null,
            cards: []
        };

        // Intersection Observer
        this.observer = null;

        // 初始化
        this.init();
    }

    /**
     * 初始化组件
     */
    init() {
        try {
            // 1. 获取DOM元素
            this._getDOMElements();

            // 2. 准备业务数据
            this._prepareBusinessData();

            // 3. 渲染业务卡片
            this._renderBusinessCards();

            // 4. 设置动画观察器
            this._setupIntersectionObserver();

            // 5. 绑定事件
            this._bindEvents();

            this.state.isInitialized = true;
            console.log('💼 业务卡片组件初始化完成');

        } catch (error) {
            console.error('❌ 业务卡片组件初始化失败:', error);
        }
    }

    /**
     * 获取DOM元素
     * @private
     */
    _getDOMElements() {
        this.elements.container = document.getElementById(this.config.containerId);
        
        if (!this.elements.container) {
            throw new Error(`业务卡片容器 #${this.config.containerId} 未找到`);
        }
    }

    /**
     * 准备业务数据
     * @private
     */
    _prepareBusinessData() {
        this.state.businessData = [
            {
                id: 'risk-screening',
                title: '公积金贷款贷前风险筛查',
                subtitle: '大数据+AI智能风控',
                description: '运用大数据、AI技术手段，共同开发优化公积金贷款的贷前风险筛查模型，提高审批效率，降低信贷风险。',
                features: [
                    '大数据风险评估',
                    'AI智能决策引擎',
                    '多维度信用分析',
                    '实时风险监控'
                ],
                icon: '🔍',
                color: 'blue',
                stats: {
                    label: '风险识别准确率',
                    value: '95%+'
                }
            },
            {
                id: 'commercial-to-public',
                title: '公积金商转公担保业务',
                subtitle: '无缝转贷解决方案',
                description: '提供阶段性担保，简化"先还后贷"流程，实现无需自筹资金结清商贷即可完成转贷，加速审批与发放。',
                features: [
                    '无需自筹资金',
                    '简化转贷流程',
                    '阶段性担保服务',
                    '快速审批放款'
                ],
                icon: '🔄',
                color: 'green',
                stats: {
                    label: '平均办理时长',
                    value: '3-5天'
                }
            },
            {
                id: 'second-hand-guarantee',
                title: '公积金二手房担保业务',
                subtitle: '全程资金监管保障',
                description: '二手房公积金贷款实行交易资金全额监管，通过阶段性担保提前发放贷款，确保房产交易真实性和完整性。',
                features: [
                    '交易资金监管',
                    '阶段性担保',
                    '流程全程管控',
                    '风险有效防控'
                ],
                icon: '🏠',
                color: 'orange',
                stats: {
                    label: '资金安全保障',
                    value: '100%'
                }
            },
            {
                id: 'asset-management',
                title: '贷后资产委托管理',
                subtitle: '专业资产管理服务',
                description: '提供专业的贷后资产管理服务，包括资产监控、逾期催收、资产处置等，减轻管理负担，提升资产质量。',
                features: [
                    '资产实时监控',
                    '专业逾期催收',
                    '资产处置服务',
                    '风险预警机制'
                ],
                icon: '📊',
                color: 'purple',
                stats: {
                    label: '资产回收率',
                    value: '98%+'
                }
            }
        ];
    }

    /**
     * 渲染业务卡片
     * @private
     */
    _renderBusinessCards() {
        const cardsHTML = this.state.businessData.map((business, index) => 
            this._renderBusinessCard(business, index)
        ).join('');

        this.elements.container.innerHTML = cardsHTML;
        
        // 更新卡片元素引用
        this.elements.cards = this.elements.container.querySelectorAll('.business-card');
    }

    /**
     * 渲染单个业务卡片
     * @private
     */
    _renderBusinessCard(business, index) {
        return `
            <div class="business-card business-card--${business.color}" data-index="${index}" data-business-id="${business.id}">
                <div class="business-card__inner">
                    <!-- 正面 -->
                    <div class="business-card__front">
                        <div class="business-card__header">
                            <div class="business-card__icon">${business.icon}</div>
                            <div class="business-card__stats">
                                <span class="stats-value">${business.stats.value}</span>
                                <span class="stats-label">${business.stats.label}</span>
                            </div>
                        </div>
                        <div class="business-card__content">
                            <h3 class="business-card__title">${business.title}</h3>
                            <p class="business-card__subtitle">${business.subtitle}</p>
                            <p class="business-card__description">${business.description}</p>
                        </div>
                        <div class="business-card__footer">
                            <button class="business-card__flip-btn" aria-label="查看详细功能">
                                <span>查看详情</span>
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                    <path d="M8 12l-4-4h8l-4 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 背面 -->
                    <div class="business-card__back">
                        <div class="business-card__back-header">
                            <h4>核心功能特色</h4>
                            <button class="business-card__close-btn" aria-label="返回">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                    <path d="M12 4L4 12M4 4l8 8" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                            </button>
                        </div>
                        <ul class="business-card__features">
                            ${business.features.map(feature => `
                                <li class="feature-item">
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                        <path d="M13.5 3.5L6 11 2.5 7.5"/>
                                    </svg>
                                    <span>${feature}</span>
                                </li>
                            `).join('')}
                        </ul>
                        <div class="business-card__back-footer">
                            <button class="business-card__contact-btn">
                                立即咨询
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 设置Intersection Observer
     * @private
     */
    _setupIntersectionObserver() {
        if (!window.IntersectionObserver) {
            // 如果不支持Intersection Observer，直接显示所有卡片
            this._showAllCards();
            return;
        }

        this.observer = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this._animateBusinessCard(entry.target);
                    }
                });
            },
            {
                threshold: this.config.observerThreshold,
                rootMargin: '50px'
            }
        );

        // 观察所有业务卡片
        this.elements.cards.forEach(card => {
            this.observer.observe(card);
        });
    }

    /**
     * 动画显示业务卡片
     * @private
     */
    _animateBusinessCard(element) {
        const index = parseInt(element.dataset.index);
        
        if (this.state.animatedCards.has(index)) {
            return; // 已经动画过了
        }

        this.state.animatedCards.add(index);

        // 添加动画延迟
        setTimeout(() => {
            element.classList.add('business-card--animated');
        }, index * this.config.animationDelay);
    }

    /**
     * 显示所有卡片（降级方案）
     * @private
     */
    _showAllCards() {
        this.elements.cards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('business-card--animated');
            }, index * this.config.animationDelay);
        });
    }

    /**
     * 绑定事件
     * @private
     */
    _bindEvents() {
        // 卡片翻转事件
        if (this.config.enableFlipEffect) {
            this.elements.container.addEventListener('click', this._handleCardClick.bind(this));
        }

        // 联系按钮事件
        this.elements.container.addEventListener('click', this._handleContactClick.bind(this));

        // 窗口大小调整事件
        window.addEventListener('resize', this._handleResize.bind(this));
    }

    /**
     * 处理卡片点击事件
     * @private
     */
    _handleCardClick(event) {
        const flipBtn = event.target.closest('.business-card__flip-btn');
        const closeBtn = event.target.closest('.business-card__close-btn');
        
        if (flipBtn) {
            const card = event.target.closest('.business-card');
            this._flipCard(card, true);
        } else if (closeBtn) {
            const card = event.target.closest('.business-card');
            this._flipCard(card, false);
        }
    }

    /**
     * 翻转卡片
     * @private
     */
    _flipCard(cardElement, toBack = true) {
        const index = parseInt(cardElement.dataset.index);
        
        if (toBack) {
            cardElement.classList.add('business-card--flipped');
            this.state.flippedCards.add(index);
        } else {
            cardElement.classList.remove('business-card--flipped');
            this.state.flippedCards.delete(index);
        }
    }

    /**
     * 处理联系按钮点击
     * @private
     */
    _handleContactClick(event) {
        const contactBtn = event.target.closest('.business-card__contact-btn');
        
        if (contactBtn) {
            const card = event.target.closest('.business-card');
            const businessId = card.dataset.businessId;
            
            // 滚动到联系表单
            this._scrollToContact(businessId);
        }
    }

    /**
     * 滚动到联系表单
     * @private
     */
    _scrollToContact(businessId) {
        const contactSection = document.getElementById('contact');
        if (contactSection) {
            contactSection.scrollIntoView({ 
                behavior: 'smooth',
                block: 'start'
            });
            
            // 触发自定义事件，传递业务类型
            window.dispatchEvent(new CustomEvent('yinke:contact-request', {
                detail: { businessId }
            }));
        }
    }

    /**
     * 处理窗口大小调整
     * @private
     */
    _handleResize() {
        // 在移动端自动关闭所有翻转的卡片
        if (window.innerWidth < 768) {
            this.state.flippedCards.forEach(index => {
                const card = this.elements.cards[index];
                if (card) {
                    card.classList.remove('business-card--flipped');
                }
            });
            this.state.flippedCards.clear();
        }
    }

    /**
     * 销毁组件
     */
    destroy() {
        // 断开Intersection Observer
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }

        // 移除事件监听器
        this.elements.container.removeEventListener('click', this._handleCardClick.bind(this));
        this.elements.container.removeEventListener('click', this._handleContactClick.bind(this));
        window.removeEventListener('resize', this._handleResize.bind(this));

        // 清空容器
        if (this.elements.container) {
            this.elements.container.innerHTML = '';
        }

        console.log('🗑️ 业务卡片组件已销毁');
    }

    /**
     * 获取当前状态
     * @returns {Object} 当前状态
     */
    getState() {
        return { ...this.state };
    }

    /**
     * 处理窗口大小调整（公共方法）
     */
    handleResize() {
        this._handleResize();
    }
}

// 导出为全局变量和模块
if (typeof window !== 'undefined') {
    window.BusinessCards = BusinessCards;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = BusinessCards;
}
