/**
 * stagewise-init.js
 * Stagewise开发工具初始化脚本（非模块化版本）
 */

// 仅在开发环境中初始化
const isDevelopment = () => {
  return (
    window.location.hostname === 'localhost' || 
    window.location.hostname === '127.0.0.1' ||
    window.location.hostname.includes('.local')
  );
};

// 初始化Stagewise工具栏
const initStagewise = () => {
  if (isDevelopment()) {
    // 创建script标签动态加载ESM模块
    const script = document.createElement('script');
    script.type = 'module';
    script.textContent = `
      import { initToolbar } from 'https://esm.sh/@stagewise/toolbar';
      
      const stagewiseConfig = {
        plugins: []
      };
      
      initToolbar(stagewiseConfig);
      console.log('Stagewise开发工具栏已初始化');
    `;
    document.head.appendChild(script);
  }
};

// 页面加载完成后初始化
window.addEventListener('DOMContentLoaded', initStagewise); 