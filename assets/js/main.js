/**
 * main.js
 * 湖北银科官网主入口文件，负责初始化和协调所有模块
 * 
 * {{CHENGQI:
 * Action: Added
 * Timestamp: 2025-01-11 15:30:00 +08:00
 * Reason: Per P1-LD-001 创建主逻辑入口文件，协调所有核心模块和组件
 * Principle_Applied: SOLID - 依赖倒置原则，通过接口协调各模块；KISS - 保持主逻辑简洁清晰
 * Optimization: 实现模块化初始化，支持渐进式加载和错误处理
 * Architectural_Note (AR): 作为应用的入口点，负责模块间的协调和生命周期管理
 * Documentation_Note (DW): 相关架构文档见 /project_document/architecture/final_architecture_v1.0.md
 * }}
 */

class YinkeWebsite {
    constructor() {
        // 应用状态
        this.state = {
            isInitialized: false,
            isLoading: true,
            performanceLevel: 'medium',
            modules: {
                performanceDetector: null,
                resourceManager: null,
                scrollEngine: null,
                heroAnimation: null,
                timelineSection: null,
                businessCards: null,
                dataVisualization: null,
                contactForm: null,
                sectionBackgroundManager: null
            }
        };

        // 配置
        this.config = {
            debug: false, // 生产环境设为false
            enableAnimations: true,
            resourceBasePath: 'assets/',
            loadingTimeout: 30000
        };

        // 绑定方法
        this._handleLoadingComplete = this._handleLoadingComplete.bind(this);
        this._handlePerformanceDetected = this._handlePerformanceDetected.bind(this);
        this._handleError = this._handleError.bind(this);

        // 开始初始化
        this.init();
    }

    /**
     * 初始化应用
     */
    async init() {
        try {
            console.log('🚀 湖北银科官网初始化开始...');

            // 1. 初始化性能检测
            await this._initPerformanceDetector();

            // 2. 初始化资源管理器
            await this._initResourceManager();

            // 3. 预加载关键资源
            await this._preloadCriticalResources();

            // 4. 初始化核心模块
            await this._initCoreModules();

            // 5. 初始化页面组件
            await this._initPageComponents();

            // 6. 绑定全局事件
            this._bindGlobalEvents();

            // 7. 完成初始化
            this._completeInitialization();

        } catch (error) {
            this._handleError('初始化失败', error);
        }
    }

    /**
     * 初始化性能检测器
     * @private
     */
    async _initPerformanceDetector() {
        if (!window.PerformanceDetector) {
            throw new Error('PerformanceDetector 模块未加载');
        }

        this.state.modules.performanceDetector = new PerformanceDetector({
            onComplete: this._handlePerformanceDetected,
            debug: this.config.debug
        });

        // 等待性能检测完成
        const performanceLevel = await this.state.modules.performanceDetector.runTest();
        this.state.performanceLevel = performanceLevel;

        console.log(`📊 设备性能级别: ${performanceLevel}`);
    }

    /**
     * 初始化资源管理器
     * @private
     */
    async _initResourceManager() {
        if (!window.ResourceManager) {
            throw new Error('ResourceManager 模块未加载');
        }

        this.state.modules.resourceManager = new ResourceManager({
            basePath: this.config.resourceBasePath,
            onProgress: this._updateLoadingProgress.bind(this),
            onComplete: this._handleLoadingComplete,
            debug: this.config.debug
        });

        console.log('📦 资源管理器初始化完成');
    }

    /**
     * 预加载关键资源
     * @private
     */
    async _preloadCriticalResources() {
        const resourceManager = this.state.modules.resourceManager;

        // 根据性能级别决定加载策略
        if (this.state.performanceLevel === 'high') {
            // 高性能设备：加载完整动画序列
            resourceManager.addImageSequence(
                'images/hero-sequence',
                'webp',
                1,
                60,
                'frame_',
                '',
                3,
                1 // 最高优先级
            );
        } else if (this.state.performanceLevel === 'medium' || this.state.performanceLevel === 'low') {
            // 中等性能设备：加载简化动画序列
            resourceManager.addImageSequence(
                'images/hero-sequence',
                'webp',
                1,
                2,
                'frame_',
                '',
                3,
                1
            );
        } else {
            // 低性能设备：只加载静态图片
            resourceManager.add('images/hero-static.webp', 'image', 'hero-static', 1);
        }

        // 添加其他关键资源（暂时注释掉缺失的图片）
        resourceManager
            .add('images/icons/logo.svg', 'image', 'logo', 2);
            // .add('images/backgrounds/hero-bg.webp', 'image', 'hero-bg', 3)
            // .add('images/backgrounds/section-bg-1.webp', 'image', 'section-bg-1', 4)
            // .add('images/backgrounds/section-bg-2.webp', 'image', 'section-bg-2', 5);

        // 开始加载
        console.log('⏳ 开始预加载关键资源...');
        return resourceManager.loadAll();
    }

    /**
     * 初始化核心模块
     * @private
     */
    async _initCoreModules() {
        // 初始化滚动动画引擎
        if (window.ScrollAnimationEngine) {
            this.state.modules.scrollEngine = new ScrollAnimationEngine({
                debug: this.config.debug
            });
            console.log('🎬 滚动动画引擎初始化完成');
        }
    }

    /**
     * 初始化页面组件
     * @private
     */
    async _initPageComponents() {
        // 初始化首屏动画组件
        if (window.HeroAnimation) {
            this.state.modules.heroAnimation = new HeroAnimation({
                performanceLevel: this.state.performanceLevel,
                resourceManager: this.state.modules.resourceManager,
                scrollEngine: this.state.modules.scrollEngine,
                showTechElements: false // 设置为 true 可启用网格和科技元素
            });
        }

        // 初始化时间轴组件
        if (window.TimelineSection) {
            this.state.modules.timelineSection = new TimelineSection();
        }

        // 初始化业务卡片组件
        if (window.BusinessCards) {
            this.state.modules.businessCards = new BusinessCards();
        }

        // 初始化数据可视化组件
        if (window.DataVisualization) {
            this.state.modules.dataVisualization = new DataVisualization();
        }

        // 初始化联系表单组件
        if (window.ContactForm) {
            this.state.modules.contactForm = new ContactForm();
        }

        // 初始化Section背景管理器
        if (window.SectionBackgroundManager) {
            this.state.modules.sectionBackgroundManager = new SectionBackgroundManager({
                resourceManager: this.state.modules.resourceManager,
                debug: this.config.debug
            });
        }

        console.log('🧩 页面组件初始化完成');
    }

    /**
     * 绑定全局事件
     * @private
     */
    _bindGlobalEvents() {
        // 导航菜单事件
        this._initNavigation();

        // 平滑滚动事件
        this._initSmoothScroll();

        // 导航栏滚动效果
        this._initScrollNavigation();

        // 窗口调整大小事件
        window.addEventListener('resize', this._handleResize.bind(this));

        console.log('🔗 全局事件绑定完成');
    }

    /**
     * 完成初始化
     * @private
     */
    _completeInitialization() {
        this.state.isInitialized = true;
        this.state.isLoading = false;

        // 隐藏加载屏幕
        this._hideLoadingScreen();

        console.log('✅ 湖北银科官网初始化完成');

        // 触发自定义事件
        window.dispatchEvent(new CustomEvent('yinke:initialized', {
            detail: { website: this }
        }));
    }

    /**
     * 更新加载进度
     * @private
     */
    _updateLoadingProgress(progress, loaded, total) {
        const progressBar = document.getElementById('progress-fill');
        if (progressBar) {
            progressBar.style.width = `${progress * 100}%`;
        }

        const loadingText = document.querySelector('.loading-text');
        if (loadingText) {
            loadingText.textContent = `正在加载银科官网... ${Math.round(progress * 100)}%`;
        }
    }

    /**
     * 处理加载完成
     * @private
     */
    _handleLoadingComplete(result) {
        console.log('📦 资源加载完成:', result);
        
        if (result.failed > 0) {
            console.warn(`⚠️ ${result.failed} 个资源加载失败`);
        }
    }

    /**
     * 处理性能检测完成
     * @private
     */
    _handlePerformanceDetected(level, metrics) {
        console.log('📊 性能检测完成:', { level, metrics });
    }

    /**
     * 处理错误
     * @private
     */
    _handleError(message, error) {
        console.error(`❌ ${message}:`, error);

        // 显示错误信息给用户
        this._showErrorMessage(message);
    }

    /**
     * 初始化导航菜单
     * @private
     */
    _initNavigation() {
        const navToggle = document.getElementById('nav-toggle');
        const navMenu = document.querySelector('.nav-menu');

        if (navToggle && navMenu) {
            navToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
                navToggle.classList.toggle('active');
            });
        }

        // 导航链接点击事件
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                this._scrollToSection(targetId);

                // 移动端关闭菜单
                if (navMenu.classList.contains('active')) {
                    navMenu.classList.remove('active');
                    navToggle.classList.remove('active');
                }
            });
        });
    }

    /**
     * 初始化平滑滚动
     * @private
     */
    _initSmoothScroll() {
        // 为所有内部链接添加平滑滚动
        const internalLinks = document.querySelectorAll('a[href^="#"]');
        internalLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                this._scrollToSection(targetId);
            });
        });
    }

    /**
     * 初始化导航栏滚动效果
     * @private
     */
    _initScrollNavigation() {
        const mainNav = document.getElementById('main-navigation');
        if (!mainNav) {
            console.warn('导航栏元素未找到');
            return;
        }

        let lastScrollY = window.scrollY;
        let ticking = false;

        const updateNavigation = () => {
            const currentScrollY = window.scrollY;

            // 滚动超过50px时添加scrolled类
            if (currentScrollY > 50) {
                mainNav.classList.add('scrolled');
            } else {
                mainNav.classList.remove('scrolled');
            }

            // 更新活动导航链接
            this._updateActiveNavLink();

            lastScrollY = currentScrollY;
            ticking = false;
        };

        const onScroll = () => {
            if (!ticking) {
                requestAnimationFrame(updateNavigation);
                ticking = true;
            }
        };

        // 绑定滚动事件
        window.addEventListener('scroll', onScroll, { passive: true });

        // 初始检查
        updateNavigation();
    }

    /**
     * 更新活动导航链接
     * @private
     */
    _updateActiveNavLink() {
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.nav-link');

        let currentSection = '';
        const scrollPosition = window.scrollY + 100; // 偏移量考虑导航栏高度

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;

            if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                currentSection = section.getAttribute('id');
            }
        });

        // 更新导航链接的活动状态
        navLinks.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');
            if (href === `#${currentSection}`) {
                link.classList.add('active');
            }
        });
    }

    /**
     * 滚动到指定区域
     * @private
     */
    _scrollToSection(sectionId) {
        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
            // 获取导航栏高度
            const mainNav = document.getElementById('main-navigation');
            const navHeight = mainNav ? mainNav.offsetHeight : 80;

            const offsetTop = targetSection.offsetTop - navHeight - 20; // 额外20px间距
            window.scrollTo({
                top: Math.max(0, offsetTop), // 确保不会滚动到负值
                behavior: 'smooth'
            });
        }
    }

    /**
     * 处理窗口大小调整
     * @private
     */
    _handleResize() {
        // 通知所有组件窗口大小已改变
        Object.values(this.state.modules).forEach(module => {
            if (module && typeof module.handleResize === 'function') {
                module.handleResize();
            }
        });
    }

    /**
     * 隐藏加载屏幕
     * @private
     */
    _hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            setTimeout(() => {
                loadingScreen.classList.add('hidden');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }, 1000);
        }
    }

    /**
     * 显示错误信息
     * @private
     */
    _showErrorMessage(message) {
        // 创建错误提示元素
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `
            <div class="error-content">
                <h3>加载出现问题</h3>
                <p>${message}</p>
                <button onclick="location.reload()">重新加载</button>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(errorDiv);

        // 3秒后自动隐藏
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }

    /**
     * 获取模块实例
     * @param {string} moduleName 模块名称
     * @returns {Object|null} 模块实例
     */
    getModule(moduleName) {
        return this.state.modules[moduleName] || null;
    }

    /**
     * 获取应用状态
     * @returns {Object} 应用状态
     */
    getState() {
        return { ...this.state };
    }
}

// 全局函数，供HTML中的按钮调用
function scrollToSection(sectionId) {
    if (window.yinkeWebsite) {
        window.yinkeWebsite._scrollToSection(sectionId);
    }
}

// DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    // 创建全局应用实例
    window.yinkeWebsite = new YinkeWebsite();
});

// 导出为模块（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = YinkeWebsite;
}
